#!/usr/bin/env python3
"""
Quick test of the simplified interface.
"""

import sys
import os
sys.path.append('bin')

def quick_test():
    """Quick test of basic functionality."""
    
    print("Quick Test of Simplified Interface")
    print("=" * 40)
    
    try:
        from igbert_model import IgBertModel
        
        print("✓ IgBert imported")
        
        model = IgBertModel()
        print("✓ IgBert model created")
        
        # Test sequences
        heavy = "EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMH"
        light = "DIQMTQSPSSLSASVGDRITITCRASQAFDNYVA"
        
        print(f"Heavy: {heavy}")
        print(f"Light: {light}")
        
        # Test encoding
        result = model.encode(heavy_chain=heavy, light_chain=light)
        print(f"✓ Encoding successful: {type(result)}")
        
        if isinstance(result, dict):
            print(f"Keys: {list(result.keys())}")
            for k, v in result.items():
                print(f"  {k}: {v.shape}")
        
        # Test decoding
        decoded = model.decode(result)
        print(f"✓ Decoding successful: {type(decoded)}")
        
        if isinstance(decoded, dict):
            print(f"Decoded keys: {list(decoded.keys())}")
            for k, v in decoded.items():
                print(f"  {k}: {v}")
                print(f"  {k} length: {len(v)}")
                
                # Compare with original
                original = heavy if k == 'heavy' else light
                print(f"  {k} match: {v == original}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = quick_test()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
