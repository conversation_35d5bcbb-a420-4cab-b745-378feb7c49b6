name: efficient-evolution
channels:
  - pytorch
  - pyg
  - bioconda
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotlipy=0.7.0=py39hb9d737c_1004
  - ca-certificates=2023.01.10=h06a4308_0
  - certifi=2022.12.7=py39h06a4308_0
  - cffi=1.15.1=py39h74dc2b5_0
  - charset-normalizer=2.1.1=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - cryptography=3.4.8=py39h95dcef6_1
  - cudatoolkit=11.3.1=h2bc3f7f_2
  - hhsuite=3.3.0=py39pl5321h67e14b5_5
  - hmmer=3.3.2=h87f3376_2
  - idna=3.4=pyhd8ed1ab_0
  - intel-openmp=2022.1.0=h9e868ea_3769
  - jinja2=3.1.2=pyhd8ed1ab_1
  - joblib=1.2.0=pyhd8ed1ab_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libblas=3.9.0=16_linux64_mkl
  - libcblas=3.9.0=16_linux64_mkl
  - libffi=3.3=he6710b0_2
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=12.2.0=h69a702a_19
  - libgfortran5=12.2.0=h337968e_19
  - libgomp=11.2.0=h1234567_1
  - liblapack=3.9.0=16_linux64_mkl
  - libnsl=2.0.0=h7f98852_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - mafft=7.310=h87f3376_5
  - markupsafe=2.1.1=py39hb9d737c_1
  - mkl=2022.1.0=hc2b9512_224
  - ncurses=6.3=h5eee18b_3
  - openssl=1.1.1t=h7f8727e_0
  - perl=5.32.1=2_h7f98852_perl5
  - pip=22.2.2=py39h06a4308_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pyg=2.1.0=py39_torch_1.12.0_cu113
  - pyopenssl=20.0.1=pyhd8ed1ab_0
  - pyparsing=3.0.9=pyhd8ed1ab_0
  - pysocks=1.7.1=pyha2e5f31_6
  - python=3.9.13=haa1d7c7_2
  - python_abi=3.9=2_cp39
  - pytorch=1.12.1=py3.9_cuda11.3_cudnn8.3.2_0
  - pytorch-cluster=1.6.0=py39_torch_1.12.0_cu113
  - pytorch-mutex=1.0=cuda
  - pytorch-scatter=2.0.9=py39_torch_1.12.0_cu113
  - pytorch-sparse=0.6.15=py39_torch_1.12.0_cu113
  - readline=8.2=h5eee18b_0
  - requests=2.28.1=pyhd8ed1ab_1
  - scikit-learn=1.0.2=py39h4dfa638_0
  - setuptools=65.5.0=py39h06a4308_0
  - six=1.16.0=pyh6c4a22f_0
  - sqlite=3.39.3=h5082296_0
  - threadpoolctl=3.1.0=pyh8a188c0_0
  - tk=8.6.12=h1ccaba5_0
  - tqdm=4.64.1=pyhd8ed1ab_0
  - typing_extensions=4.3.0=py39h06a4308_0
  - tzdata=2022f=h04d1e81_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.6=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - pip:
    - anndata==0.8.0
    - attrs==22.2.0
    - biopython==1.79
    - biotite==0.35.0
    - bleach==5.0.1
    - build==0.9.0
    - clize==5.0.0
    - commonmark==0.9.1
    - contourpy==1.0.6
    - cycler==0.11.0
    - docutils==0.19
    - evolocity==1.0
    - exceptiongroup==1.1.0
    - fair-esm==2.0.0
    - fonttools==4.38.0
    - h5py==3.7.0
    - igraph==0.10.2
    - importlib-metadata==5.2.0
    - iniconfig==1.1.1
    - jaraco-classes==3.2.3
    - jeepney==0.8.0
    - keyring==23.13.1
    - kiwisolver==1.4.4
    - llvmlite==0.39.1
    - louvain==0.8.0
    - matplotlib==3.6.2
    - more-itertools==9.0.0
    - msgpack==1.0.4
    - natsort==8.2.0
    - networkx==2.8.8
    - numba==0.56.4
    - numpy==1.23.4
    - od==2.0.2
    - packaging==21.3
    - pandas==1.5.1
    - patsy==0.5.3
    - pep517==0.13.0
    - pillow==9.3.0
    - pkginfo==1.9.2
    - pluggy==1.0.0
    - pygments==2.13.0
    - pynndescent==0.5.8
    - pytest==7.2.0
    - python-dateutil==2.8.2
    - python-igraph==0.10.2
    - pytz==2022.6
    - readme-renderer==37.3
    - requests-toolbelt==0.10.1
    - rfc3986==2.0.0
    - rich==12.6.0
    - scanpy==1.9.1
    - scipy==1.9.3
    - seaborn==0.12.1
    - secretstorage==3.3.3
    - session-info==1.0.0
    - sigtools==4.0.1
    - statsmodels==0.13.5
    - stdlib-list==0.8.0
    - texttable==1.6.4
    - tomli==2.0.1
    - twine==4.0.2
    - umap-learn==0.5.3
    - urllib3==1.26.12
    - webencodings==0.5.1
    - zipp==3.11.0
