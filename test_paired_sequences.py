#!/usr/bin/env python3
"""
Test script to demonstrate the new paired sequence functionality for IgBert and IgT5 models.
"""

import sys
import os
sys.path.append('bin')

from igbert_model import IgBertModel
from igt5_model import IgT5Model

def test_paired_sequences():
    """Test the new paired sequence functionality."""
    
    # Example sequences
    heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
    light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"
    
    print("Testing IgBert Model")
    print("=" * 50)
    
    try:
        # Initialize IgBert model
        igbert = IgBertModel()
        
        print("1. Testing single heavy chain with auto-pairing:")
        logits1 = igbert.predict_sequence_prob(heavy_chain, is_heavy=True)
        print(f"   Heavy chain logits shape: {logits1.shape}")
        
        print("2. Testing single light chain with auto-pairing:")
        logits2 = igbert.predict_sequence_prob(light_chain, is_heavy=False)
        print(f"   Light chain logits shape: {logits2.shape}")
        
        print("3. Testing paired sequences (both provided):")
        logits3 = igbert.predict_sequence_prob(None, heavy_chain=heavy_chain, light_chain=light_chain)
        print(f"   Paired sequences logits shape: {logits3.shape}")
        
        print("4. Testing heavy chain with specific light chain:")
        logits4 = igbert.predict_sequence_prob(heavy_chain, light_chain=light_chain)
        print(f"   Heavy + specific light logits shape: {logits4.shape}")
        
        print("5. Testing light chain with specific heavy chain:")
        logits5 = igbert.predict_sequence_prob(light_chain, heavy_chain=heavy_chain)
        print(f"   Light + specific heavy logits shape: {logits5.shape}")
        
        print("6. Testing encode method with paired sequences:")
        embeddings = igbert.encode(heavy_chain, light_chain=light_chain)
        print(f"   Encode embeddings shape: {embeddings.shape}")
        
        print("IgBert tests completed successfully!")
        
    except Exception as e:
        print(f"IgBert test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("Testing IgT5 Model")
    print("=" * 50)
    
    try:
        # Initialize IgT5 model
        igt5 = IgT5Model()
        
        print("1. Testing single heavy chain with auto-pairing:")
        embeddings1 = igt5.predict_sequence_prob(heavy_chain, is_heavy=True)
        print(f"   Heavy chain embeddings shape: {embeddings1.shape}")
        
        print("2. Testing single light chain with auto-pairing:")
        embeddings2 = igt5.predict_sequence_prob(light_chain, is_heavy=False)
        print(f"   Light chain embeddings shape: {embeddings2.shape}")
        
        print("3. Testing paired sequences (both provided):")
        embeddings3 = igt5.predict_sequence_prob(None, heavy_chain=heavy_chain, light_chain=light_chain)
        print(f"   Paired sequences embeddings shape: {embeddings3.shape}")
        
        print("4. Testing heavy chain with specific light chain:")
        embeddings4 = igt5.predict_sequence_prob(heavy_chain, light_chain=light_chain)
        print(f"   Heavy + specific light embeddings shape: {embeddings4.shape}")
        
        print("5. Testing light chain with specific heavy chain:")
        embeddings5 = igt5.predict_sequence_prob(light_chain, heavy_chain=heavy_chain)
        print(f"   Light + specific heavy embeddings shape: {embeddings5.shape}")
        
        print("6. Testing encode method with paired sequences:")
        embeddings = igt5.encode(heavy_chain, light_chain=light_chain)
        print(f"   Encode embeddings shape: {embeddings.shape}")
        
        print("IgT5 tests completed successfully!")
        
    except Exception as e:
        print(f"IgT5 test failed: {e}")
        import traceback
        traceback.print_exc()

def test_decode_functionality():
    """Test the decode functionality with the new models."""
    
    print("\n" + "=" * 50)
    print("Testing Decode Functionality")
    print("=" * 50)
    
    heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
    
    try:
        # Test IgBert decode
        igbert = IgBertModel()
        logits = igbert.predict_sequence_prob(heavy_chain)
        decoded_seq = igbert.decode(logits)
        print(f"IgBert - Original: {heavy_chain[:50]}...")
        print(f"IgBert - Decoded:  {decoded_seq[:50]}...")
        print(f"IgBert - Match: {heavy_chain == decoded_seq}")
        
    except Exception as e:
        print(f"IgBert decode test failed: {e}")
    
    try:
        # Test IgT5 decode
        igt5 = IgT5Model()
        embeddings = igt5.predict_sequence_prob(heavy_chain)
        decoded_seq = igt5.decode(embeddings)
        print(f"IgT5 - Original: {heavy_chain[:50]}...")
        print(f"IgT5 - Decoded:  {decoded_seq[:50]}...")
        print(f"IgT5 - Match: {heavy_chain == decoded_seq}")
        
    except Exception as e:
        print(f"IgT5 decode test failed: {e}")

if __name__ == "__main__":
    print("Testing Paired Sequence Functionality for IgBert and IgT5")
    print("=" * 60)
    
    test_paired_sequences()
    test_decode_functionality()
    
    print("\n" + "=" * 60)
    print("All tests completed!")
