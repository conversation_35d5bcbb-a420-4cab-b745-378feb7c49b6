#!/usr/bin/env python3
"""
Test the simplified paired sequence interface.
"""

import sys
import os
sys.path.append('bin')

def test_basic_functionality():
    """Test basic functionality of the simplified interface."""
    
    print("Testing Simplified Paired Sequence Interface")
    print("=" * 50)
    
    try:
        from igbert_model import IgBertModel
        from igt5_model import IgT5Model
        
        print("✓ Models imported successfully")
        
        # Test sequences
        heavy_chain = "EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMH"
        light_chain = "DIQMTQSPSSLSASVGDRITITCRASQAFDNYVA"
        
        print(f"Heavy chain: {heavy_chain}")
        print(f"Light chain: {light_chain}")
        
        # Test IgBert
        print("\nTesting IgBert...")
        try:
            igbert = IgBertModel()
            print("✓ IgBert model created")
            
            # Test encoding
            result = igbert.encode(heavy_chain=heavy_chain, light_chain=light_chain)
            print(f"✓ Encoding result type: {type(result)}")
            
            if isinstance(result, dict):
                print(f"✓ Result is dictionary with keys: {list(result.keys())}")
                for chain_type, data in result.items():
                    print(f"  {chain_type}: shape {data.shape}")
            
            # Test decoding
            decoded = igbert.decode(result)
            print(f"✓ Decoding result type: {type(decoded)}")
            
            if isinstance(decoded, dict):
                print(f"✓ Decoded is dictionary with keys: {list(decoded.keys())}")
                for chain_type, seq in decoded.items():
                    print(f"  {chain_type}: {seq[:20]}...")
            
        except Exception as e:
            print(f"✗ IgBert test failed: {e}")
        
        # Test IgT5
        print("\nTesting IgT5...")
        try:
            igt5 = IgT5Model()
            print("✓ IgT5 model created")
            
            # Test encoding
            result = igt5.encode(heavy_chain=heavy_chain, light_chain=light_chain)
            print(f"✓ Encoding result type: {type(result)}")
            
            if isinstance(result, dict):
                print(f"✓ Result is dictionary with keys: {list(result.keys())}")
                for chain_type, data in result.items():
                    print(f"  {chain_type}: shape {data.shape}")
            
            # Test decoding
            decoded = igt5.decode(result)
            print(f"✓ Decoding result type: {type(decoded)}")
            
            if isinstance(decoded, dict):
                print(f"✓ Decoded is dictionary with keys: {list(decoded.keys())}")
                for chain_type, seq in decoded.items():
                    print(f"  {chain_type}: {seq[:20]}...")
            
        except Exception as e:
            print(f"✗ IgT5 test failed: {e}")
        
        # Test blank sequences
        print("\nTesting blank sequences...")
        try:
            igbert = IgBertModel()
            
            # Test with blank sequences
            result_blank = igbert.encode(heavy_chain="", light_chain="")
            print(f"✓ Blank sequences handled: {type(result_blank)}")
            
            # Test with only heavy chain
            result_heavy = igbert.encode(heavy_chain=heavy_chain, light_chain="")
            print(f"✓ Heavy-only handled: {type(result_heavy)}")
            
            # Test with only light chain
            result_light = igbert.encode(heavy_chain="", light_chain=light_chain)
            print(f"✓ Light-only handled: {type(result_light)}")
            
        except Exception as e:
            print(f"✗ Blank sequence test failed: {e}")
        
        print("\n✓ All basic tests completed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_amis_integration():
    """Test integration with amis.py functions."""
    
    print("\nTesting AMIS Integration")
    print("=" * 30)
    
    try:
        from amis import encode, decode, reconstruct
        from igbert_model import IgBertModel
        
        print("✓ AMIS functions imported")
        
        # Test sequences
        heavy_chain = "EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMH"
        light_chain = "DIQMTQSPSSLSASVGDRITITCRASQAFDNYVA"
        
        model = IgBertModel()
        print("✓ Model created")
        
        # Test encode function
        embeddings = encode(heavy_chain=heavy_chain, light_chain=light_chain, model=model)
        print(f"✓ AMIS encode: {type(embeddings)}")
        
        # Test decode function
        decoded = decode(embeddings, model)
        print(f"✓ AMIS decode: {type(decoded)}")
        
        # Test reconstruct function
        reconstructed = reconstruct(heavy_chain=heavy_chain, light_chain=light_chain, model=model)
        print(f"✓ AMIS reconstruct: {type(reconstructed)}")
        
        print("✓ AMIS integration tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ AMIS integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    
    print("Simplified Interface Testing Suite")
    print("=" * 60)
    
    success = True
    
    # Test basic functionality
    success &= test_basic_functionality()
    
    # Test AMIS integration
    success &= test_amis_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ All tests passed! The simplified interface is working correctly.")
        print("\nKey features verified:")
        print("- Paired sequence encoding/decoding")
        print("- Dictionary-based return values")
        print("- Blank sequence handling")
        print("- AMIS integration")
        print("\nYou can now use:")
        print("  python3 bin/test_paired_simple.py --antibody mab114 --model-name igbert")
    else:
        print("✗ Some tests failed. Please check the implementation.")
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())
