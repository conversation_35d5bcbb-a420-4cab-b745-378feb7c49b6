#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update all antibody testing scripts with the simplified paired sequence interface.
"""

import os

# Antibody data with correct sequences from the original scripts
antibodies = {
    'mab114': {
        'namespace': 'mAb114',
        'heavy': 'EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMHWVRQTIDKRLEWVSAVGPSGDTYYADSVKGRFAVSRENAKNSLSLQMNSLTAGDTAIYYCVRSDRGVAGLFDSWGQGILVTVSS',
        'light': 'DIQMTQSPSSLSASVGDRITITCRASQAFDNYVAWYQQRPGKVPKLLISAASALHAGVPSRFSGSGSGTHFTLTISSLQPEDVATYYCQNYNSAPLTFGGGTKVEIK'
    },
    'medi8852': {
        'namespace': 'MEDI8852',
        'heavy': 'QVQLQQSGPGLVKPSQTLSLTCAISGDSVSSYNAVWNWIRQSPSRGLEWLGRTYYRSGWYNDYAESVKSRITINPDTSKNQFSLQLNSVTPEDTAVYYCARSGHITVFGVNVDAFDMWGQGTMVTVSS',
        'light': 'DIQMTQSPSSLSASVGDRVTITCRTSQSLSSYTHWYQQKPGKAPKLLIYAASSRGSGVPSRFSGSGSGTDFTLTISSLQPEDFATYYCQQSRTFGQGTKVEIK'
    },
    's309': {
        'namespace': 'S309',
        'heavy': 'QVQLVQSGAEVKKPGASVKVSCKASGYPFTSYGISWVRQAPGQGLEWMGWISTYNGNTNYAQKFQGRVTMTTDTSTTTGYMELRRLRSDDTAVYYCARDYTRGAWFGESLIGGFDNWGQGTLVTVSS',
        'light': 'EIVLTQSPGTLSLSPGERATLSCRASQTVSSTSLAWYQQKPGQAPRLLIYGASSRATGIPDRFSGSGSGTDFTLTISRLEPEDFAVYYCQQHDTSLTFGGGTKVEIK'
    },
    'regn10987': {
        'namespace': 'REGN10987',
        'heavy': 'QVQLVESGGGVVQPGRSLRLSCAASGFTFSNYAMYWVRQAPGKGLEWVAVISYDGSNKYYADSVKGRFTISRDNSKNTLYLQMNSLRTEDTAVYYCASGSDYGDYLLVYWGQGTLVTVSS',
        'light': 'QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLMIYDVSKRPSGVSNRFSGSKSGNTASLTISGLQSEDEADYYCNSLTSISTWVFGGGTKLTVL'
    },
    'c143': {
        'namespace': 'C143',
        'heavy': 'EVQLVESGGGLVQPGGSLRLSCAASGFSVSTKYMTWVRQAPGKGLEWVSVLYSGGSDYYADSVKGRFTISRDNSKNALYLQMNSLRVEDTGVYYCARDSSEVRDHPGHPGRSVGAFDIWGQGTMVTVSS',
        'light': 'QSALTQPASVSGSPGQSITISCTGTSNDVGSYTLVSWYQQYPGKAPKLLIFEGTKRSSGISNRFSGSKSGNTASLTISGLQGEDEADYYCCSYAGASTFVFGGGTKLTVL'
    },
    'medi_uca': {
        'namespace': 'MEDI_UCA',
        'heavy': 'QVQLQQSGPGLVKPSQTLSLTCAISGDSVSSNSAAWNWIRQSPSRGLEWLGRTYYRSKWYNDYAVSVKSRITINPDTSKNQFSLQLNSVTPEDTAVYYCARGGHITIFGVNIDAFDIWGQGTMVTVSS',
        'light': 'DIQMTQSPSSLSASVGDRVTITCRASQSISSYLNWYQQKPGKAPKLLIYAASSLQSGVPSRFSGSGSGTDFTLTISSLQPEDFATYYCQQSRTFGQGTKVEIK'
    },
    'mab114_uca': {
        'namespace': 'mAb114_UCA',
        'heavy': 'EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYDMHWVRQATGKGLEWVSAIGTAGDTYYPGSVKGRFTISRENAKNSLYLQMNSLRAGDTAVYYCVRSDRGVAGLFDSWGQGTLVTVSS',
        'light': 'DIQMTQSPSSLSASVGDRVTITCRASQGISNYLAWYQQKPGKVPKLLIYAASTLQSGVPSRFSGSGSGTDFTLTISSLQPEDVATYYCQKYNSAPLTFGGGTKVEIK'
    }
}

def create_antibody_script(antibody_name, antibody_data):
    """Create an updated antibody script using the template."""
    
    # Read the template
    with open('bin/antibody_template.py', 'r') as f:
        template = f.read()
    
    # Replace placeholders
    script = template.replace('{ANTIBODY_NAME}', antibody_data['namespace'])
    script = script.replace('{NAMESPACE}', antibody_data['namespace'])
    script = script.replace('{HEAVY_SEQUENCE}', antibody_data['heavy'])
    script = script.replace('{LIGHT_SEQUENCE}', antibody_data['light'])
    
    return script

def update_original_scripts():
    """Update the original antibody scripts to use the simplified interface."""
    
    for antibody_name, antibody_data in antibodies.items():
        # Create the updated script
        script_content = create_antibody_script(antibody_name, antibody_data)
        
        # Write to the original script file
        script_path = f'bin/{antibody_name}.py'
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(script_path, 0o755)
        
        print(f"Updated {script_path}")

def create_eval_script():
    """Create an updated eval_models.sh script."""
    
    eval_script = '''#!/bin/bash

# Script to evaluate antibody models with simplified paired sequence functionality
# Usage: bash bin/eval_models.sh [antibody_name]

if [ $# -eq 0 ]; then
    echo "Usage: bash bin/eval_models.sh [antibody_name]"
    echo "Available antibodies: medi8852, medi_uca, mab114, mab114_uca, s309, regn10987, c143"
    exit 1
fi

# Extract antibody name without .py extension if provided
antibody_name=$(basename "$1" .py)

echo "Evaluating $antibody_name with simplified paired sequence interface"
echo "=================================================================="

# Create logs directory if it doesn't exist
mkdir -p logs

# Models to test
declare -a models=( "igbert" "igt5" "esm1b" "esm1v1" "esm2-150M" )

echo "Testing models: ${models[@]}"
echo ""

for model in "${models[@]}"
do
    echo "Running $model..."
    log_file="logs/reconstruct_${antibody_name}_${model}.log"
    
    if python3 bin/${antibody_name}.py --model-name $model > "$log_file" 2>&1; then
        echo "  ✓ $model completed successfully"
    else
        echo "  ✗ $model failed (check $log_file)"
    fi
done

echo ""
echo "Evaluation complete! Check logs/ for detailed results."
echo "Log files: logs/reconstruct_${antibody_name}_*.log"
'''
    
    with open('bin/eval_models.sh', 'w') as f:
        f.write(eval_script)
    
    os.chmod('bin/eval_models.sh', 0o755)
    print("Updated bin/eval_models.sh")

def create_test_all_script():
    """Create a script to test all antibodies."""
    
    test_all_script = '''#!/bin/bash

# Script to test all antibodies with the simplified paired sequence interface
# Usage: bash bin/test_all_antibodies_simple.sh [model_name]

MODEL=${1:-"igbert"}

echo "Testing All Antibodies with Simplified Interface"
echo "================================================"
echo "Model: $MODEL"
echo "Timestamp: $(date)"
echo ""

# List of antibodies
ANTIBODIES=("mab114" "medi8852" "s309" "regn10987" "c143" "medi_uca" "mab114_uca")

# Create logs directory
mkdir -p logs/simple

total_tests=0
passed_tests=0

for antibody in "${ANTIBODIES[@]}"; do
    echo "Testing $antibody..."
    echo "$(printf '=%.0s' {1..30})"
    
    log_file="logs/simple/${antibody}_${MODEL}.log"
    
    if python3 bin/${antibody}.py --model-name $MODEL > "$log_file" 2>&1; then
        echo "  ✓ $antibody PASSED"
        ((passed_tests++))
    else
        echo "  ✗ $antibody FAILED (check $log_file)"
    fi
    
    ((total_tests++))
    echo ""
done

echo "SUMMARY"
echo "======="
echo "Total tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"
echo "Success rate: $(echo "scale=1; $passed_tests * 100 / $total_tests" | bc)%"
echo ""
echo "Detailed logs available in: logs/simple/"
'''
    
    with open('bin/test_all_antibodies_simple.sh', 'w') as f:
        f.write(test_all_script)
    
    os.chmod('bin/test_all_antibodies_simple.sh', 0o755)
    print("Created bin/test_all_antibodies_simple.sh")

def main():
    """Update all antibody scripts."""
    
    print("Updating Antibody Scripts with Simplified Interface")
    print("=" * 55)
    
    # Update original scripts
    update_original_scripts()
    
    # Create updated eval script
    create_eval_script()
    
    # Create test all script
    create_test_all_script()
    
    print("\n" + "=" * 55)
    print("All antibody scripts updated successfully!")
    print("\nUpdated scripts:")
    for antibody_name in antibodies.keys():
        print(f"  bin/{antibody_name}.py")
    
    print("\nUpdated evaluation scripts:")
    print("  bin/eval_models.sh")
    print("  bin/test_all_antibodies_simple.sh")
    
    print("\nUsage examples:")
    print("  python3 bin/mab114.py --model-name igbert")
    print("  bash bin/eval_models.sh mab114")
    print("  bash bin/test_all_antibodies_simple.sh igbert")

if __name__ == '__main__':
    main()
