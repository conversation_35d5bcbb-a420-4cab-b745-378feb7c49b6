#!/usr/bin/env python3
"""
Examples demonstrating how to use the new paired sequence functionality 
for IgBert and IgT5 models.

This script shows different ways to provide sequences to the models:
1. Paired sequences (both heavy and light chains)
2. Single sequence with chain type specification
3. Single sequence with automatic pairing using defaults
"""

import sys
import os
sys.path.append('bin')

from igbert_model import IgBertModel
from igt5_model import IgT5Model

def example_usage():
    """Demonstrate different ways to use the paired sequence functionality."""
    
    # Example antibody sequences
    heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
    light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"
    
    print("Paired Sequence Usage Examples")
    print("=" * 50)
    
    # Initialize models
    igbert = IgBertModel()
    igt5 = IgT5Model()
    
    print("\n1. PAIRED SEQUENCES - Both heavy and light chains provided")
    print("-" * 55)
    print("Use case: You have both heavy and light chain sequences")
    print("The tokenizer expects: 'V Q ... S S </s> E V ... I K'")
    
    # For IgBert
    print("\nIgBert with paired sequences:")
    paired_sequences = []
    for sequence_heavy, sequence_light in [(heavy_chain, light_chain)]:
        paired_sequences.append(' '.join(sequence_heavy) + ' </s> ' + ' '.join(sequence_light))
    print(f"Formatted input: {paired_sequences[0][:100]}...")
    
    # Method 1: Provide both chains explicitly
    logits = igbert.predict_sequence_prob(None, heavy_chain=heavy_chain, light_chain=light_chain)
    print(f"IgBert logits shape: {logits.shape}")
    
    # Method 2: Use encode with both chains
    embeddings = igbert.encode(heavy_chain, light_chain=light_chain)
    print(f"IgBert embeddings shape: {embeddings.shape}")
    
    # For IgT5
    print("\nIgT5 with paired sequences:")
    embeddings = igt5.predict_sequence_prob(None, heavy_chain=heavy_chain, light_chain=light_chain)
    print(f"IgT5 embeddings shape: {embeddings.shape}")
    
    print("\n2. SINGLE SEQUENCE WITH CHAIN TYPE SPECIFICATION")
    print("-" * 55)
    print("Use case: You have one chain and want to specify its type")
    
    # Heavy chain analysis
    print("\nAnalyzing heavy chain (is_heavy=True):")
    heavy_logits = igbert.predict_sequence_prob(heavy_chain, is_heavy=True)
    print(f"IgBert heavy chain logits shape: {heavy_logits.shape}")
    
    heavy_embeddings = igt5.predict_sequence_prob(heavy_chain, is_heavy=True)
    print(f"IgT5 heavy chain embeddings shape: {heavy_embeddings.shape}")
    
    # Light chain analysis
    print("\nAnalyzing light chain (is_heavy=False):")
    light_logits = igbert.predict_sequence_prob(light_chain, is_heavy=False)
    print(f"IgBert light chain logits shape: {light_logits.shape}")
    
    light_embeddings = igt5.predict_sequence_prob(light_chain, is_heavy=False)
    print(f"IgT5 light chain embeddings shape: {light_embeddings.shape}")
    
    print("\n3. SINGLE SEQUENCE WITH SPECIFIC PAIRING")
    print("-" * 55)
    print("Use case: You have one chain and want to pair it with a specific partner")
    
    # Heavy chain with specific light chain
    print("\nHeavy chain with specific light chain partner:")
    logits = igbert.predict_sequence_prob(heavy_chain, light_chain=light_chain)
    print(f"IgBert logits shape: {logits.shape}")
    
    # Light chain with specific heavy chain
    print("\nLight chain with specific heavy chain partner:")
    logits = igbert.predict_sequence_prob(light_chain, heavy_chain=heavy_chain)
    print(f"IgBert logits shape: {logits.shape}")
    
    print("\n4. AUTOMATIC PAIRING WITH DEFAULTS")
    print("-" * 55)
    print("Use case: You have one chain and want automatic pairing with canonical sequences")
    
    # This is the default behavior when only seq and is_heavy are provided
    print("\nHeavy chain with default light chain pairing:")
    logits = igbert.predict_sequence_prob(heavy_chain, is_heavy=True)
    print(f"IgBert logits shape: {logits.shape}")
    print(f"Paired with default light chain: {igbert.default_light[:50]}...")
    
    print("\nLight chain with default heavy chain pairing:")
    logits = igbert.predict_sequence_prob(light_chain, is_heavy=False)
    print(f"IgBert logits shape: {logits.shape}")
    print(f"Paired with default heavy chain: {igbert.default_heavy[:50]}...")

def reconstruction_example():
    """Show how to use the models for sequence reconstruction."""
    
    print("\n" + "=" * 50)
    print("SEQUENCE RECONSTRUCTION EXAMPLES")
    print("=" * 50)
    
    heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
    
    # Initialize models
    igbert = IgBertModel()
    igt5 = IgT5Model()
    
    print("\nIgBert Reconstruction:")
    print("-" * 25)
    
    # Get logits and reconstruct
    logits = igbert.predict_sequence_prob(heavy_chain, is_heavy=True)
    reconstructed = igbert.decode(logits)
    
    print(f"Original:      {heavy_chain}")
    print(f"Reconstructed: {reconstructed}")
    print(f"Match: {heavy_chain == reconstructed}")
    
    print("\nIgT5 Reconstruction:")
    print("-" * 25)
    
    # Get embeddings and reconstruct
    embeddings = igt5.predict_sequence_prob(heavy_chain, is_heavy=True)
    reconstructed = igt5.decode(embeddings)
    
    print(f"Original:      {heavy_chain}")
    print(f"Reconstructed: {reconstructed}")
    print(f"Match: {heavy_chain == reconstructed}")

def integration_with_amis():
    """Show how the new functionality integrates with the existing amis.py interface."""
    
    print("\n" + "=" * 50)
    print("INTEGRATION WITH AMIS.PY")
    print("=" * 50)
    
    # Import amis functions
    from amis import get_model_name, encode, decode, reconstruct
    
    heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
    light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"
    
    print("\nUsing amis.py interface with paired sequences:")
    
    # Get models through amis interface
    igbert = get_model_name('igbert')
    igt5 = get_model_name('igt5')
    
    # Use encode with additional parameters
    print("\nEncoding with paired sequences:")
    embeddings1 = encode(heavy_chain, igbert, light_chain=light_chain)
    print(f"IgBert embeddings shape: {embeddings1.shape}")
    
    embeddings2 = encode(heavy_chain, igt5, light_chain=light_chain)
    print(f"IgT5 embeddings shape: {embeddings2.shape}")
    
    # Use reconstruct with chain type specification
    print("\nReconstruction with chain type:")
    reconstructed1 = reconstruct(heavy_chain, igbert, encode_kwargs={'is_heavy': True})
    print(f"IgBert reconstructed: {reconstructed1[:50]}...")
    
    reconstructed2 = reconstruct(light_chain, igt5, encode_kwargs={'is_heavy': False})
    print(f"IgT5 reconstructed: {reconstructed2[:50]}...")

if __name__ == "__main__":
    print("Paired Sequence Functionality Examples")
    print("=" * 60)
    print("This script demonstrates the new paired sequence capabilities")
    print("for IgBert and IgT5 models.")
    
    try:
        example_usage()
        reconstruction_example()
        integration_with_amis()
        
        print("\n" + "=" * 60)
        print("All examples completed successfully!")
        print("\nKey takeaways:")
        print("- Both models now support paired heavy/light chain input")
        print("- Users can specify chain types or provide both chains")
        print("- Automatic pairing with canonical sequences when needed")
        print("- Full integration with existing amis.py interface")
        
    except Exception as e:
        print(f"\nExample failed: {e}")
        import traceback
        traceback.print_exc()
