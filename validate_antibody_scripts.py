#!/usr/bin/env python3
"""
Validation script to check that all antibody paired scripts are properly created
and have the correct structure.
"""

import os
import sys
import importlib.util

def validate_script(script_path):
    """Validate that a script has the correct structure."""
    
    if not os.path.exists(script_path):
        return False, f"File does not exist: {script_path}"
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Check for required functions
        required_functions = [
            'test_paired_sequences',
            'test_single_sequences', 
            'test_auto_pairing',
            'test_traditional_mode',
            'parse_args'
        ]
        
        missing_functions = []
        for func in required_functions:
            if f"def {func}(" not in content:
                missing_functions.append(func)
        
        if missing_functions:
            return False, f"Missing functions: {', '.join(missing_functions)}"
        
        # Check for required imports
        required_imports = [
            'from utils import *',
            'from amis import',
            'import time',
            'import traceback'
        ]
        
        missing_imports = []
        for imp in required_imports:
            if imp not in content:
                missing_imports.append(imp)
        
        if missing_imports:
            return False, f"Missing imports: {', '.join(missing_imports)}"
        
        # Check for sequence definitions
        if 'vh = (' not in content or 'vl = (' not in content:
            return False, "Missing sequence definitions (vh/vl)"
        
        # Check for test mode handling
        test_modes = ['paired', 'single', 'auto', 'traditional', 'all']
        mode_checks = [f"args.test_mode == '{mode}'" for mode in test_modes]
        
        if not any(check in content for check in mode_checks):
            return False, "Missing test mode handling"
        
        return True, "Script structure is valid"
        
    except Exception as e:
        return False, f"Error reading script: {e}"

def validate_syntax(script_path):
    """Check if the script has valid Python syntax."""
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Try to compile the script
        compile(content, script_path, 'exec')
        return True, "Syntax is valid"
        
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, f"Error checking syntax: {e}"

def main():
    """Validate all antibody paired scripts."""
    
    print("Validating Antibody Paired Scripts")
    print("=" * 40)
    
    # List of expected scripts
    antibodies = [
        'mab114', 'medi8852', 's309', 'regn10987', 
        'c143', 'medi_uca', 'mab114_uca'
    ]
    
    all_valid = True
    
    for antibody in antibodies:
        script_path = f'bin/{antibody}_paired.py'
        print(f"\nValidating {script_path}...")
        
        # Check structure
        structure_valid, structure_msg = validate_script(script_path)
        print(f"  Structure: {'✓' if structure_valid else '✗'} {structure_msg}")
        
        if not structure_valid:
            all_valid = False
            continue
        
        # Check syntax
        syntax_valid, syntax_msg = validate_syntax(script_path)
        print(f"  Syntax: {'✓' if syntax_valid else '✗'} {syntax_msg}")
        
        if not syntax_valid:
            all_valid = False
            continue
        
        # Check if executable
        is_executable = os.access(script_path, os.X_OK)
        print(f"  Executable: {'✓' if is_executable else '✗'} {'Yes' if is_executable else 'No'}")
        
        if not is_executable:
            print(f"    Run: chmod +x {script_path}")
    
    # Validate supporting scripts
    print(f"\nValidating supporting scripts...")
    
    supporting_scripts = [
        'bin/eval_paired_models.sh',
        'bin/test_all_antibodies.sh',
        'bin/compare_paired_results.py'
    ]
    
    for script in supporting_scripts:
        exists = os.path.exists(script)
        executable = os.access(script, os.X_OK) if exists else False
        
        print(f"  {script}: {'✓' if exists else '✗'} {'Exists' if exists else 'Missing'}")
        if exists:
            print(f"    Executable: {'✓' if executable else '✗'} {'Yes' if executable else 'No'}")
            if not executable:
                print(f"    Run: chmod +x {script}")
    
    print(f"\n" + "=" * 40)
    if all_valid:
        print("✓ All antibody scripts are valid!")
        print("\nYou can now run:")
        print("  bash bin/test_all_antibodies.sh quick")
        print("  bash bin/eval_paired_models.sh mab114.py")
        print("  python3 bin/mab114_paired.py --model-name igbert --test-mode paired")
    else:
        print("✗ Some scripts have issues. Please fix them before running tests.")
    
    return all_valid

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
