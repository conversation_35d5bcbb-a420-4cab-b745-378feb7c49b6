# Antibody Paired Sequence Testing Guide

This guide explains how to use the enhanced antibody testing scripts that robustly test the new paired sequence functionality for IgBert and IgT5 models.

## Overview

The testing suite includes:
- **Enhanced antibody scripts** with comprehensive paired sequence testing
- **Automated test runners** for batch testing
- **Comparison tools** for analyzing results
- **Multiple testing modes** for different use cases

## Available Antibodies

The following antibody scripts are available for testing:
- `mab114_paired.py` - mAb114 antibody
- `medi8852_paired.py` - MEDI8852 antibody  
- `s309_paired.py` - S309 antibody
- `regn10987_paired.py` - REGN10987 antibody
- `c143_paired.py` - C143 antibody
- `medi_uca_paired.py` - MEDI UCA antibody
- `mab114_uca_paired.py` - mAb114 UCA antibody

## Testing Modes

Each antibody script supports multiple testing modes:

### 1. Paired Mode (`--test-mode paired`)
Tests paired sequence functionality where both heavy and light chains are provided:
- Both chains provided explicitly
- Heavy chain with specific light chain
- Light chain with specific heavy chain

### 2. Single Mode (`--test-mode single`)
Tests single sequence with chain type specification:
- Heavy chain analysis (is_heavy=True)
- Light chain analysis (is_heavy=False)

### 3. Auto Mode (`--test-mode auto`)
Tests automatic pairing with canonical sequences:
- Heavy chain with default light chain pairing
- Light chain with default heavy chain pairing

### 4. Traditional Mode (`--test-mode traditional`)
Tests traditional single-chain reconstruction for comparison:
- Standard heavy chain reconstruction
- Standard light chain reconstruction

### 5. All Mode (`--test-mode all`)
Runs all testing modes sequentially for comprehensive analysis.

## Usage Examples

### Single Antibody Testing

Test a specific antibody with a specific model and mode:
```bash
# Test mAb114 with IgBert in paired mode
python3 bin/mab114_paired.py --model-name igbert --test-mode paired

# Test S309 with IgT5 in all modes
python3 bin/s309_paired.py --model-name igt5 --test-mode all

# Test with traditional ESM model
python3 bin/medi8852_paired.py --model-name esm1b --test-mode traditional
```

### Batch Testing with eval_paired_models.sh

Test an antibody with multiple models and modes:
```bash
# Test mAb114 with all supported models and modes
bash bin/eval_paired_models.sh mab114.py

# Test S309 with comprehensive analysis
bash bin/eval_paired_models.sh s309.py
```

This script automatically:
- Tests paired models (IgBert, IgT5) with paired, single, and auto modes
- Tests traditional models (ESM variants) with traditional mode
- Generates detailed logs for each test
- Creates comparison reports

### Comprehensive Testing with test_all_antibodies.sh

Test all antibodies with different testing strategies:

```bash
# Quick test: Core functionality only
bash bin/test_all_antibodies.sh quick

# Full test: All models and modes (takes longer)
bash bin/test_all_antibodies.sh full

# Test only paired sequence functionality
bash bin/test_all_antibodies.sh paired-only

# Test only traditional functionality for comparison
bash bin/test_all_antibodies.sh traditional-only
```

## Output and Logs

### Log Files
All test results are saved in `logs/paired/` with the naming convention:
```
reconstruct_{antibody}_{model}_{mode}.log
```

Examples:
- `reconstruct_mab114_igbert_paired.log`
- `reconstruct_s309_igt5_single.log`
- `reconstruct_medi8852_esm1b_traditional.log`

### Comparison Reports
Detailed analysis reports are generated in `logs/paired/summaries/`:
```
{antibody}_report.txt
```

These reports include:
- Success/failure statistics
- Timing analysis
- Error summaries
- Recommendations

### Manual Report Generation
Generate a comparison report for specific antibody:
```bash
python3 bin/compare_paired_results.py --antibody mab114 --output my_report.txt
```

## Supported Models

### Paired Sequence Models
- **igbert** - IgBert model with paired sequence support
- **igt5** - IgT5 model with paired sequence support

### Traditional Models (for comparison)
- **esm1b** - ESM-1b model
- **esm1v1** - ESM-1v variant 1
- **esm2-150M** - ESM-2 150M parameter model
- **esm2-650M** - ESM-2 650M parameter model

## Expected Results

### Successful Tests
When tests pass, you should see:
- Timing information for each operation
- Mutation comparisons between original and reconstructed sequences
- Success indicators for each testing mode
- Overall analysis completion confirmation

### Common Issues
- **Model loading failures**: Ensure models are properly installed
- **Memory issues**: Large models may require significant RAM
- **CUDA errors**: GPU models require compatible CUDA installation
- **Import errors**: Ensure all dependencies are installed

## Performance Expectations

### Timing Guidelines
- **IgBert/IgT5**: 1-10 seconds per test (depending on sequence length)
- **ESM models**: 5-30 seconds per test (depending on model size)
- **Full antibody test**: 2-10 minutes (depending on modes tested)
- **All antibodies quick test**: 10-30 minutes

### Memory Requirements
- **IgBert/IgT5**: 2-4 GB RAM
- **ESM-1b/1v**: 4-8 GB RAM  
- **ESM-2 models**: 8-16 GB RAM (larger models need more)

## Troubleshooting

### Validation
Check that all scripts are properly set up:
```bash
python3 validate_antibody_scripts.py
```

### Individual Testing
Test basic functionality first:
```bash
python3 simple_test.py
```

### Verbose Output
Enable detailed error reporting:
```bash
python3 bin/mab114_paired.py --model-name igbert --test-mode paired --verbose
```

### Log Analysis
Check specific log files for detailed error information:
```bash
cat logs/paired/reconstruct_mab114_igbert_paired.log
```

## Integration with Existing Workflow

### Original Scripts
The original antibody scripts (e.g., `mab114.py`) continue to work unchanged.

### New Functionality
The paired scripts (`*_paired.py`) provide enhanced testing while maintaining compatibility with the existing `amis.py` interface.

### Backward Compatibility
All existing functionality is preserved. The new features are additive and don't break existing workflows.

## Best Practices

1. **Start with quick tests** to verify basic functionality
2. **Use verbose mode** when debugging issues
3. **Check logs** for detailed error information
4. **Compare results** between different models and modes
5. **Run validation** before extensive testing
6. **Monitor memory usage** with large models

## Summary

The enhanced antibody testing suite provides comprehensive validation of the new paired sequence functionality while maintaining compatibility with existing workflows. Use the appropriate testing mode and model combination for your specific needs, and refer to the generated reports for detailed analysis of results.
