# Paired Sequence Support for IgBert and IgT5 Models

This document describes the new paired sequence functionality implemented for IgBert and IgT5 models, allowing users to work with heavy/light chain antibody sequences in a more flexible and intuitive way.

## Overview

Both IgBert and IgT5 models now support multiple input modes for handling antibody sequences:

1. **Paired sequences**: Provide both heavy and light chains explicitly
2. **Single sequence with pairing**: Provide one chain and specify a partner
3. **Single sequence with auto-pairing**: Provide one chain and let the model use canonical defaults
4. **Chain type specification**: Specify whether a sequence is heavy or light chain

## Key Features

- **Flexible input modes** for different use cases
- **Automatic pairing** with canonical sequences when needed
- **Consistent interface** between IgBert and IgT5 models
- **Backward compatibility** with existing code
- **Proper tokenization** using `</s>` separator as expected by the models

## Usage Examples

### 1. Paired Sequences (Both Heavy and Light Chains)

When you have both heavy and light chain sequences:

```python
from igbert_model import IgBertModel
from igt5_model import IgT5Model

# Initialize models
igbert = IgBertModel()
igt5 = IgT5Model()

# Your sequences
heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"

# Method 1: Provide both chains explicitly
logits = igbert.predict_sequence_prob(None, heavy_chain=heavy_chain, light_chain=light_chain)
embeddings = igt5.predict_sequence_prob(None, heavy_chain=heavy_chain, light_chain=light_chain)

# Method 2: Use encode method
logits = igbert.encode(heavy_chain, light_chain=light_chain)
embeddings = igt5.encode(heavy_chain, light_chain=light_chain)
```

### 2. Single Sequence with Chain Type Specification

When you have one chain and want to specify its type:

```python
# Heavy chain analysis
heavy_logits = igbert.predict_sequence_prob(heavy_chain, is_heavy=True)
heavy_embeddings = igt5.predict_sequence_prob(heavy_chain, is_heavy=True)

# Light chain analysis  
light_logits = igbert.predict_sequence_prob(light_chain, is_heavy=False)
light_embeddings = igt5.predict_sequence_prob(light_chain, is_heavy=False)
```

### 3. Single Sequence with Specific Pairing

When you have one chain and want to pair it with a specific partner:

```python
# Heavy chain with specific light chain
logits = igbert.predict_sequence_prob(heavy_chain, light_chain=light_chain)

# Light chain with specific heavy chain
logits = igbert.predict_sequence_prob(light_chain, heavy_chain=heavy_chain)
```

### 4. Integration with Existing amis.py Interface

The new functionality integrates seamlessly with the existing amis.py interface:

```python
from amis import get_model_name, encode, decode, reconstruct

# Get models
igbert = get_model_name('igbert')
igt5 = get_model_name('igt5')

# Use with additional parameters
embeddings = encode(heavy_chain, igbert, light_chain=light_chain)
reconstructed = reconstruct(heavy_chain, igbert, encode_kwargs={'is_heavy': True})
```

## Method Signatures

### IgBertModel and IgT5Model

Both models now have consistent method signatures:

```python
def predict_sequence_prob(self, seq, light_chain=None, heavy_chain=None, is_heavy=True):
    """
    Args:
        seq: The primary input sequence to analyze
        light_chain: Optional light chain sequence for pairing
        heavy_chain: Optional heavy chain sequence for pairing
        is_heavy: If True, treat seq as heavy chain; if False, treat as light chain
                 Only used when neither light_chain nor heavy_chain is provided
    """

def encode(self, seq, light_chain=None, heavy_chain=None, is_heavy=True):
    """
    Same parameters as predict_sequence_prob
    """
```

## Input Processing Logic

The models handle different input scenarios as follows:

1. **Both heavy_chain and light_chain provided**: Use the provided pair, seq is ignored
2. **Only heavy_chain provided**: Treat seq as light chain
3. **Only light_chain provided**: Treat seq as heavy chain  
4. **Neither provided**: Use seq with is_heavy flag to determine pairing

## Tokenization Format

The models format sequences as expected by the tokenizers:

```
"V Q ... S S </s> E V ... I K"
```

Where:
- Heavy chain amino acids are space-separated
- `</s>` separates heavy and light chains
- Light chain amino acids are space-separated

## Default Canonical Sequences

When automatic pairing is needed, the models use these canonical sequences:

- **Default Heavy Chain**: `"QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"`
- **Default Light Chain**: `"EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"`

## Backward Compatibility

The changes maintain backward compatibility:

- Existing code using `model.encode(seq)` will continue to work
- The `is_heavy=True` default ensures heavy chain behavior by default
- All existing amis.py functionality is preserved

## Testing

Run the provided test scripts to verify functionality:

```bash
# Basic functionality test
python3 simple_test.py

# Comprehensive examples
python3 paired_sequence_examples.py

# Full test suite (requires model downloads)
python3 test_paired_sequences.py
```

## Notes

- The models will automatically download from Hugging Face on first use
- GPU acceleration is used automatically if CUDA is available
- Both models handle sequence length mismatches with padding/truncation
- IgBert returns logits suitable for masked language modeling
- IgT5 returns embeddings suitable for similarity-based analysis
