# Simplified Paired Sequence Interface for IgBert and IgT5 Models

This document describes the simplified paired sequence functionality implemented for IgBert and IgT5 models, allowing users to work with heavy/light chain antibody sequences efficiently.

## Overview

Both IgBert and IgT5 models now support a simplified paired sequence interface:

- **Paired sequence input**: Provide both heavy and light chains for optimal analysis
- **Blank sequence handling**: Use empty strings for missing chains
- **Dictionary-based returns**: Get results for both chains in a structured format
- **Consistent interface** between IgBert and IgT5 models
- **Backward compatibility** with existing code

## Key Features

- **Simplified API** with just two parameters: `heavy_chain` and `light_chain`
- **Automatic tokenization** with proper sequence separators ([SEP] for IgBert, </s> for IgT5)
- **Dictionary returns** with 'heavy' and 'light' keys
- **Mutation analysis** built into antibody testing scripts
- **Fast performance** optimized for production use

## Basic Usage

```python
from igbert_model import IgBertModel
from igt5_model import IgT5Model

# Initialize models
igbert = IgBertModel()
igt5 = IgT5Model()

# Your sequences
heavy_chain = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTTDTSTTTGYMELRRLRSDDTAVYYCAR"
light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASSRATGIPDRFSGSGSGTDFTLTISRLEPEDFAVYYCQQYSNWPLTFGGGTKVEIK"

# Encode paired sequences
igbert_result = igbert.encode(heavy_chain=heavy_chain, light_chain=light_chain)
igt5_result = igt5.encode(heavy_chain=heavy_chain, light_chain=light_chain)

# Results are dictionaries with 'heavy' and 'light' keys
print(f"IgBert heavy shape: {igbert_result['heavy'].shape}")
print(f"IgBert light shape: {igbert_result['light'].shape}")

# Decode back to sequences
igbert_decoded = igbert.decode(igbert_result)
igt5_decoded = igt5.decode(igt5_result)

print(f"Decoded heavy: {igbert_decoded['heavy']}")
print(f"Decoded light: {igbert_decoded['light']}")
```

## Method Signatures

### IgBertModel and IgT5Model

Both models have consistent method signatures:

```python
def encode(self, heavy_chain="", light_chain=""):
    """
    Encode paired sequences.
    
    Args:
        heavy_chain: Heavy chain sequence (default: empty string)
        light_chain: Light chain sequence (default: empty string)
        
    Returns:
        Dictionary with 'heavy' and/or 'light' results
    """

def decode(self, result_dict):
    """
    Decode results back to sequences.
    
    Args:
        result_dict: Dictionary with 'heavy' and/or 'light' data
        
    Returns:
        Dictionary with 'heavy' and/or 'light' sequences
    """
```

## Integration with AMIS

The simplified interface integrates seamlessly with the existing amis.py functions:

```python
from amis import encode, decode, reconstruct

# Using amis functions
result = encode(heavy_chain=heavy_seq, light_chain=light_seq, model=model)
decoded = decode(result, model)
reconstructed = reconstruct(heavy_chain=heavy_seq, light_chain=light_seq, model=model)
```

## Antibody Testing Scripts

All antibody testing scripts have been updated to use the simplified interface:

### Available Scripts
- `bin/mab114.py` - mAb114 antibody analysis
- `bin/medi8852.py` - MEDI8852 antibody analysis
- `bin/s309.py` - S309 antibody analysis
- `bin/regn10987.py` - REGN10987 antibody analysis
- `bin/c143.py` - C143 antibody analysis
- `bin/medi_uca.py` - MEDI UCA antibody analysis
- `bin/mab114_uca.py` - mAb114 UCA antibody analysis

### Usage Examples

```bash
# Test individual antibody with specific model
python3 bin/mab114.py --model-name igbert
python3 bin/s309.py --model-name igt5

# Test antibody with multiple models
bash bin/eval_models.sh mab114

# Test all antibodies with specific model
bash bin/test_all_antibodies_simple.sh igbert
```

## Expected Output

When running antibody tests, you'll see:

1. **Sequence Information**: Heavy and light chain lengths
2. **Encoding/Decoding**: Processing time and success indicators
3. **Mutation Analysis**: Detailed comparison showing predicted mutations
4. **Summary**: Overall success status and execution time

Example output:
```
S309 Paired Sequence Analysis
Model: igbert
Heavy chain length: 127
Light chain length: 107

Testing Paired Sequence Reconstruction
==================================================
1. Encoding paired sequences...
2. Decoding paired sequences...
   Time: 3.14s

Heavy Chain Analysis:
Heavy mutations: P28T, G79A, R85S, N116Y

Light Chain Analysis:
Light mutations: H92Y

✓ S309 analysis completed successfully
  Execution time: 3.14s
  Model type: paired
```

## Performance

- **IgBert**: ~0.5-3 seconds per antibody
- **IgT5**: ~1-5 seconds per antibody  
- **Traditional models**: ~2-10 seconds per antibody

## Supported Models

### Paired Sequence Models
- **igbert** - IgBert model with paired sequence support
- **igt5** - IgT5 model with paired sequence support

### Traditional Models (single chain)
- **esm1b** - ESM-1b model
- **esm1v1** - ESM-1v variant 1
- **esm2-150M** - ESM-2 150M parameter model

## Migration from Old Interface

The new simplified interface replaces the complex multi-mode system with a clean, straightforward API:

### Old (Complex)
```python
# Multiple confusing parameters
result = model.predict_sequence_prob(seq, light_chain=lc, heavy_chain=hc, is_heavy=True)
```

### New (Simplified)
```python
# Clean, intuitive interface
result = model.encode(heavy_chain=hc, light_chain=lc)
```

## Troubleshooting

### Common Issues
- **Empty results**: Check that sequences are provided as strings
- **Shape mismatches**: Ensure sequences contain valid amino acids
- **Memory errors**: Use shorter sequences or smaller models for testing

### Validation
```python
# Check if model supports paired sequences
if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
    result = model.encode(heavy_chain=heavy, light_chain=light)
else:
    # Traditional single-chain approach
    result = model.encode(heavy)
```

This simplified interface provides a clean, efficient way to work with paired antibody sequences while maintaining full compatibility with existing workflows.
