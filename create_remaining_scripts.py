#!/usr/bin/env python3
"""
Create the remaining antibody paired scripts with correct sequences.
"""

import os

# Antibody sequences from the original scripts
antibodies = {
    'regn10987': {
        'namespace': 'REGN10987',
        'vh': 'QVQLVESGGGVVQPGRSLRLSCAASGFTFSNYAMYWVRQAPGKGLEWVAVISYDGSNKYYADSVKGRFTISRDNSKNTLYLQMNSLRTEDTAVYYCASGSDYGDYLLVYWGQGTLVTVSS',
        'vl': 'QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLMIYDVSKRPSGVSNRFSGSKSGNTASLTISGLQSEDEADYYCNSLTSISTWVFGGGTKLTVL'
    },
    'c143': {
        'namespace': 'C143',
        'vh': 'EVQLVESGGGLVQPGGSLRLSCAASGFSVSTKYMTWVRQAPGKGLEWVSVLYSGGSDYYADSVKGRFTISRDNSKNALYLQMNSLRVEDTGVYYCARDSSEVRDHPGHPGRSVGAFDIWGQGTMVTVSS',
        'vl': 'QSALTQPASVSGSPGQSITISCTGTSNDVGSYTLVSWYQQYPGKAPKLLIFEGTKRSSGISNRFSGSKSGNTASLTISGLQGEDEADYYCCSYAGASTFVFGGGTKLTVL'
    },
    'medi_uca': {
        'namespace': 'MEDI_UCA',
        'vh': 'QVQLQQSGPGLVKPSQTLSLTCAISGDSVSSNSAAWNWIRQSPSRGLEWLGRTYYRSKWYNDYAVSVKSRITINPDTSKNQFSLQLNSVTPEDTAVYYCARGGHITIFGVNIDAFDIWGQGTMVTVSS',
        'vl': 'DIQMTQSPSSLSASVGDRVTITCRASQSISSYLNWYQQKPGKAPKLLIYAASSLQSGVPSRFSGSGSGTDFTLTISSLQPEDFATYYCQQSRTFGQGTKVEIK'
    },
    'mab114_uca': {
        'namespace': 'MAB114_UCA',
        'vh': 'EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYDMHWVRQATGKGLEWVSAIGTAGDTYYPGSVKGRFTISRENAKNSLYLQMNSLRAGDTAVYYCVRSDRGVAGLFDSWGQGTLVTVSS',
        'vl': 'DIQMTQSPSSLSASVGDRVTITCRASQGISNYLAWYQQKPGKVPKLLIYAASTLQSGVPSRFSGSGSGTDFTLTISSLQPEDVATYYCQKYNSAPLTFGGGTKVEIK'
    }
}

def create_script(antibody_name, data):
    """Create a paired script for the given antibody."""
    
    # Read the template from s309_paired.py
    with open('bin/s309_paired.py', 'r') as f:
        template = f.read()
    
    # Replace S309-specific content
    script = template.replace('S309', data['namespace'])
    script = script.replace('s309', antibody_name.lower())
    
    # Replace the sequences
    old_vh = "QVQLVQSGAEVKKPGASVKVSCKASGYPFTSYGISWVRQAPGQGLEWMGWISTYNGNTNYAQKFQGRVTMTTDTSTTTGYMELRRLRSDDTAVYYCARDYTRGAWFGESLIGGFDNWGQGTLVTVSS"
    old_vl = "EIVLTQSPGTLSLSPGERATLSCRASQTVSSTSLAWYQQKPGQAPRLLIYGASSRATGIPDRFSGSGSGTDFTLTISRLEPEDFAVYYCQQHDTSLTFGGGTKVEIK"
    
    script = script.replace(old_vh, data['vh'])
    script = script.replace(old_vl, data['vl'])
    
    # Update the description
    script = script.replace('Enhanced S309 analysis', f'Enhanced {data["namespace"]} analysis')
    script = script.replace('S309 paired sequence analysis', f'{data["namespace"]} paired sequence analysis')
    script = script.replace('S309 Paired Sequence Analysis', f'{data["namespace"]} Paired Sequence Analysis')
    script = script.replace('# S309 sequences', f'# {data["namespace"]} sequences')
    
    return script

def main():
    """Create all remaining antibody scripts."""
    
    for antibody_name, data in antibodies.items():
        script_content = create_script(antibody_name, data)
        filename = f'bin/{antibody_name}_paired.py'
        
        with open(filename, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(filename, 0o755)
        
        print(f"Created {filename}")

if __name__ == '__main__':
    main()
    print("All remaining antibody paired scripts created successfully!")
