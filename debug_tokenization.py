#!/usr/bin/env python3
"""
Debug script to understand how the tokenizer handles paired sequences.
"""

import sys
import os
sys.path.append('bin')

def debug_tokenization():
    """Debug the tokenization process."""
    
    print("Debugging Tokenization")
    print("=" * 30)
    
    try:
        from igbert_model import IgBertModel
        
        model = IgBertModel()
        print("✓ IgBert model loaded")
        
        # Test sequences
        heavy_chain = "EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMH"
        light_chain = "DIQMTQSPSSLSASVGDRITITCRASQAFDNYVA"
        
        print(f"Heavy chain: {heavy_chain}")
        print(f"Light chain: {light_chain}")
        print(f"Heavy length: {len(heavy_chain)}")
        print(f"Light length: {len(light_chain)}")
        
        # Format as paired sequence
        heavy_spaced = ' '.join(heavy_chain)
        light_spaced = ' '.join(light_chain)
        paired_seq = heavy_spaced + ' </s> ' + light_spaced
        
        print(f"\nPaired sequence: {paired_seq}")
        print(f"Paired length: {len(paired_seq)}")
        
        # Tokenize
        tokens = model.tokenizer_.batch_encode_plus(
            [paired_seq],
            add_special_tokens=True,
            padding=True,
            return_tensors="pt",
            return_special_tokens_mask=True
        )
        
        input_ids = tokens['input_ids'][0]
        print(f"\nInput IDs shape: {input_ids.shape}")
        print(f"Input IDs: {input_ids}")
        
        # Find EOS token
        eos_token_id = model.tokenizer_.eos_token_id
        print(f"\nEOS token ID: {eos_token_id}")
        
        # Find EOS positions
        eos_positions = []
        for i, token_id in enumerate(input_ids):
            if token_id == eos_token_id:
                eos_positions.append(i)
                print(f"Found EOS at position {i}")
        
        # Decode tokens to see what they represent
        print(f"\nToken by token decode:")
        for i, token_id in enumerate(input_ids):
            token = model.tokenizer_.decode([token_id])
            print(f"Position {i}: {token_id} -> '{token}'")
        
        # Show the split
        if eos_positions:
            eos_pos = eos_positions[0]
            print(f"\nSplitting at EOS position {eos_pos}:")
            print(f"Heavy tokens (1:{eos_pos}): {input_ids[1:eos_pos]}")
            print(f"Light tokens ({eos_pos+1}:-1): {input_ids[eos_pos+1:-1]}")
            
            # Decode each part
            heavy_tokens = input_ids[1:eos_pos]
            light_tokens = input_ids[eos_pos+1:-1]
            
            heavy_decoded = model.tokenizer_.decode(heavy_tokens)
            light_decoded = model.tokenizer_.decode(light_tokens)
            
            print(f"\nDecoded heavy: '{heavy_decoded}'")
            print(f"Decoded light: '{light_decoded}'")
            
            # Remove spaces and compare
            heavy_clean = heavy_decoded.replace(' ', '')
            light_clean = light_decoded.replace(' ', '')
            
            print(f"\nCleaned heavy: '{heavy_clean}'")
            print(f"Cleaned light: '{light_clean}'")
            print(f"Heavy match: {heavy_clean == heavy_chain}")
            print(f"Light match: {light_clean == light_chain}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_tokenization()
