#!/bin/bash

# Script to evaluate antibody models with simplified paired sequence functionality
# Usage: bash bin/eval_models.sh [antibody_name]

if [ $# -eq 0 ]; then
    echo "Usage: bash bin/eval_models.sh [antibody_name]"
    echo "Available antibodies: medi8852, medi_uca, mab114, mab114_uca, s309, regn10987, c143"
    exit 1
fi

# Extract antibody name without .py extension if provided
antibody_name=$(basename "$1" .py)

echo "Evaluating $antibody_name with simplified paired sequence interface"
echo "=================================================================="

# Create logs directory if it doesn't exist
mkdir -p logs

# Models to test
declare -a models=( "igbert" "igt5" "esm1b" "esm1v1" "esm2-150M" )

echo "Testing models: ${models[@]}"
echo ""

for model in "${models[@]}"
do
    echo "Running $model..."
    log_file="logs/reconstruct_${antibody_name}_${model}.log"
    
    if python3 bin/${antibody_name}.py --model-name $model > "$log_file" 2>&1; then
        echo "  ✓ $model completed successfully"
    else
        echo "  ✗ $model failed (check $log_file)"
    fi
done

echo ""
echo "Evaluation complete! Check logs/ for detailed results."
echo "Log files: logs/reconstruct_${antibody_name}_*.log"
