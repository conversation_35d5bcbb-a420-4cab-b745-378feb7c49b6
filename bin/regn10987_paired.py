#!/usr/bin/env python3
"""
Enhanced REGN10987 analysis with paired sequence functionality testing.
Tests different modes of sequence input for IgBert and IgT5 models.
"""

from utils import *
from amis import (
    get_model,
    encode,
    decode,
    deep_mutational_scan,
    compare,
    evolve,
    interpolate,
    extrapolate,
    reconstruct,
    reconstruct_prose,
)
import time
import traceback

def parse_args():
    import argparse
    parser = argparse.ArgumentParser(description='REGN10987 paired sequence analysis')
    parser.add_argument('--namespace', type=str, default='REGN10987',
                        help='Model namespace')
    parser.add_argument('--model-name', type=str, default='igbert',
                        help='Type of language model (e.g., igbert, igt5, esm1b)')
    parser.add_argument('--test-mode', type=str, default='paired',
                        choices=['paired', 'single', 'auto', 'traditional', 'all'],
                        help='Testing mode for paired sequences')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    args = parser.parse_args()
    return args

def test_paired_sequences(model, vh, vl, verbose=False):
    """Test paired sequence functionality."""
    print("=" * 60)
    print("TESTING PAIRED SEQUENCE MODE")
    print("=" * 60)
    
    results = {}
    
    if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
        try:
            print("1. Testing with both chains provided explicitly:")
            start_time = time.time()
            
            # Method 1: Both chains provided
            vh_logits = model.predict_sequence_prob(None, heavy_chain=vh, light_chain=vl)
            vh_reconstructed = model.decode(vh_logits)
            
            elapsed = time.time() - start_time
            print(f"   Time: {elapsed:.2f}s")
            print(f"   VH logits shape: {vh_logits.shape}")
            compare(vh, vh_reconstructed, namespace='Paired VH')
            
            results['paired_vh'] = {
                'original': vh,
                'reconstructed': vh_reconstructed,
                'time': elapsed,
                'shape': vh_logits.shape
            }
            
            # Method 2: Heavy chain with specific light chain
            print("\n2. Testing heavy chain with specific light chain:")
            start_time = time.time()
            vh_logits2 = model.predict_sequence_prob(vh, light_chain=vl)
            vh_reconstructed2 = model.decode(vh_logits2)
            elapsed = time.time() - start_time
            
            print(f"   Time: {elapsed:.2f}s")
            compare(vh, vh_reconstructed2, namespace='VH+specific VL')
            
            results['vh_with_vl'] = {
                'original': vh,
                'reconstructed': vh_reconstructed2,
                'time': elapsed
            }
            
            # Method 3: Light chain with specific heavy chain
            print("\n3. Testing light chain with specific heavy chain:")
            start_time = time.time()
            vl_logits = model.predict_sequence_prob(vl, heavy_chain=vh)
            vl_reconstructed = model.decode(vl_logits)
            elapsed = time.time() - start_time
            
            print(f"   Time: {elapsed:.2f}s")
            compare(vl, vl_reconstructed, namespace='VL+specific VH')
            
            results['vl_with_vh'] = {
                'original': vl,
                'reconstructed': vl_reconstructed,
                'time': elapsed
            }
            
            if verbose:
                print(f"\nDetailed comparison:")
                print(f"Method 1 vs Method 2 VH match: {vh_reconstructed == vh_reconstructed2}")
                
        except Exception as e:
            print(f"Error in paired sequence testing: {e}")
            if verbose:
                traceback.print_exc()
            results['error'] = str(e)
    else:
        print("Model does not support paired sequence functionality.")
        results['error'] = "Model does not support paired sequences"
    
    return results

def test_single_sequences(model, vh, vl, verbose=False):
    """Test single sequence with chain type specification."""
    print("\n" + "=" * 60)
    print("TESTING SINGLE SEQUENCE MODE")
    print("=" * 60)
    
    results = {}
    
    if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
        try:
            print("1. Testing heavy chain (is_heavy=True):")
            start_time = time.time()
            vh_logits = model.predict_sequence_prob(vh, is_heavy=True)
            vh_reconstructed = model.decode(vh_logits)
            elapsed = time.time() - start_time
            
            print(f"   Time: {elapsed:.2f}s")
            compare(vh, vh_reconstructed, namespace='Single VH')
            
            results['single_vh'] = {
                'original': vh,
                'reconstructed': vh_reconstructed,
                'time': elapsed
            }
            
            print("\n2. Testing light chain (is_heavy=False):")
            start_time = time.time()
            vl_logits = model.predict_sequence_prob(vl, is_heavy=False)
            vl_reconstructed = model.decode(vl_logits)
            elapsed = time.time() - start_time
            
            print(f"   Time: {elapsed:.2f}s")
            compare(vl, vl_reconstructed, namespace='Single VL')
            
            results['single_vl'] = {
                'original': vl,
                'reconstructed': vl_reconstructed,
                'time': elapsed
            }
            
        except Exception as e:
            print(f"Error in single sequence testing: {e}")
            if verbose:
                traceback.print_exc()
            results['error'] = str(e)
    else:
        print("Model does not support single sequence with chain type specification.")
        results['error'] = "Model does not support single sequence mode"
    
    return results

def test_auto_pairing(model, vh, vl, verbose=False):
    """Test automatic pairing with canonical sequences."""
    print("\n" + "=" * 60)
    print("TESTING AUTO-PAIRING MODE")
    print("=" * 60)
    
    results = {}
    
    if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
        try:
            print("1. Heavy chain with auto-pairing (default light chain):")
            start_time = time.time()
            vh_logits = model.predict_sequence_prob(vh, is_heavy=True)
            vh_reconstructed = model.decode(vh_logits)
            elapsed = time.time() - start_time
            
            print(f"   Time: {elapsed:.2f}s")
            print(f"   Paired with default light: {model.default_light[:50]}...")
            compare(vh, vh_reconstructed, namespace='Auto VH')
            
            results['auto_vh'] = {
                'original': vh,
                'reconstructed': vh_reconstructed,
                'time': elapsed,
                'default_partner': model.default_light
            }
            
            print("\n2. Light chain with auto-pairing (default heavy chain):")
            start_time = time.time()
            vl_logits = model.predict_sequence_prob(vl, is_heavy=False)
            vl_reconstructed = model.decode(vl_logits)
            elapsed = time.time() - start_time
            
            print(f"   Time: {elapsed:.2f}s")
            print(f"   Paired with default heavy: {model.default_heavy[:50]}...")
            compare(vl, vl_reconstructed, namespace='Auto VL')
            
            results['auto_vl'] = {
                'original': vl,
                'reconstructed': vl_reconstructed,
                'time': elapsed,
                'default_partner': model.default_heavy
            }
            
        except Exception as e:
            print(f"Error in auto-pairing testing: {e}")
            if verbose:
                traceback.print_exc()
            results['error'] = str(e)
    else:
        print("Model does not support auto-pairing functionality.")
        results['error'] = "Model does not support auto-pairing"
    
    return results

def test_traditional_mode(model, vh, vl, verbose=False):
    """Test traditional single-chain reconstruction."""
    print("\n" + "=" * 60)
    print("TESTING TRADITIONAL MODE")
    print("=" * 60)
    
    results = {}
    
    try:
        print("1. Heavy chain reconstruction:")
        start_time = time.time()
        vh_new = reconstruct(vh, model, encode_kwargs={'is_heavy': True}, decode_kwargs={'exclude': 'unnatural'})
        elapsed = time.time() - start_time
        
        print(f"   Time: {elapsed:.2f}s")
        compare(vh, vh_new, namespace='Traditional VH')
        
        results['traditional_vh'] = {
            'original': vh,
            'reconstructed': vh_new,
            'time': elapsed
        }
        
        print("\n2. Light chain reconstruction:")
        start_time = time.time()
        vl_new = reconstruct(vl, model, encode_kwargs={'is_heavy': False}, decode_kwargs={'exclude': 'unnatural'})
        elapsed = time.time() - start_time
        
        print(f"   Time: {elapsed:.2f}s")
        compare(vl, vl_new, namespace='Traditional VL')
        
        results['traditional_vl'] = {
            'original': vl,
            'reconstructed': vl_new,
            'time': elapsed
        }
        
    except Exception as e:
        print(f"Error in traditional mode testing: {e}")
        if verbose:
            traceback.print_exc()
        results['error'] = str(e)
    
    return results

if __name__ == '__main__':
    args = parse_args()

    print(f"REGN10987 Paired Sequence Analysis")
    print(f"Model: {args.model_name}")
    print(f"Test Mode: {args.test_mode}")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    try:
        model = get_model(args)
        print(f"Model loaded: {model.name_}")
    except Exception as e:
        print(f"Failed to load model: {e}")
        exit(1)

    # REGN10987 sequences
    vh = (
        'QVQLVESGGGVVQPGRSLRLSCAASGFTFSNYAMYWVRQAPGKGLEWVAVISYDGSNKYYADSVKGRFTISRDNSKNTLYLQMNSLRTEDTAVYYCASGSDYGDYLLVYWGQGTLVTVSS'
    )
    vl = (
        'QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLMIYDVSKRPSGVSNRFSGSKSGNTASLTISGLQSEDEADYYCNSLTSISTWVFGGGTKLTVL'
    )

    print(f"VH length: {len(vh)}")
    print(f"VL length: {len(vl)}")

    all_results = {}

    # Run tests based on mode
    if args.test_mode == 'paired' or args.test_mode == 'all':
        all_results['paired'] = test_paired_sequences(model, vh, vl, args.verbose)

    if args.test_mode == 'single' or args.test_mode == 'all':
        all_results['single'] = test_single_sequences(model, vh, vl, args.verbose)

    if args.test_mode == 'auto' or args.test_mode == 'all':
        all_results['auto'] = test_auto_pairing(model, vh, vl, args.verbose)

    if args.test_mode == 'traditional' or args.test_mode == 'all':
        all_results['traditional'] = test_traditional_mode(model, vh, vl, args.verbose)

    print(f"\n" + "=" * 60)
    print("ANALYSIS COMPLETE")
    print("=" * 60)
    
    # Summary
    for mode, results in all_results.items():
        if 'error' not in results:
            print(f"{mode.upper()} mode: SUCCESS")
        else:
            print(f"{mode.upper()} mode: FAILED - {results['error']}")
