#!/bin/bash

# Comprehensive test runner for all antibody paired sequence functionality
# Usage: bash bin/test_all_antibodies.sh [mode]
# Modes: quick, full, paired-only, traditional-only

set -e  # Exit on any error

# Configuration
ANTIBODIES=("mab114" "medi8852" "s309" "regn10987" "c143" "medi_uca" "mab114_uca")
PAIRED_MODELS=("igbert" "igt5")
TRADITIONAL_MODELS=("esm1b" "esm1v1" "esm2-150M")
TEST_MODES=("paired" "single" "auto" "traditional")

# Default mode
MODE=${1:-"quick"}

echo "Antibody Paired Sequence Testing Suite"
echo "======================================"
echo "Mode: $MODE"
echo "Timestamp: $(date)"
echo ""

# Create logs directory
mkdir -p logs/paired
mkdir -p logs/paired/summaries

# Function to run a single test
run_test() {
    local antibody=$1
    local model=$2
    local test_mode=$3
    local log_file="logs/paired/reconstruct_${antibody}_${model}_${test_mode}.log"
    
    echo "  Testing $antibody with $model ($test_mode mode)..."
    
    if python3 bin/${antibody}_paired.py --model-name $model --test-mode $test_mode > "$log_file" 2>&1; then
        echo "    ✓ SUCCESS"
        return 0
    else
        echo "    ✗ FAILED (check $log_file)"
        return 1
    fi
}

# Function to test an antibody with all specified models and modes
test_antibody() {
    local antibody=$1
    local models=("${@:2}")
    
    echo "Testing $antibody..."
    echo "$(printf '=%.0s' {1..40})"
    
    local total_tests=0
    local passed_tests=0
    
    for model in "${models[@]}"; do
        echo "Model: $model"
        
        case $MODE in
            "quick")
                # Quick test: just paired mode for paired models, traditional for others
                if [[ " ${PAIRED_MODELS[@]} " =~ " ${model} " ]]; then
                    if run_test "$antibody" "$model" "paired"; then
                        ((passed_tests++))
                    fi
                    ((total_tests++))
                else
                    if run_test "$antibody" "$model" "traditional"; then
                        ((passed_tests++))
                    fi
                    ((total_tests++))
                fi
                ;;
                
            "full")
                # Full test: all modes for all models
                for test_mode in "${TEST_MODES[@]}"; do
                    if run_test "$antibody" "$model" "$test_mode"; then
                        ((passed_tests++))
                    fi
                    ((total_tests++))
                done
                ;;
                
            "paired-only")
                # Only test paired sequence functionality
                if [[ " ${PAIRED_MODELS[@]} " =~ " ${model} " ]]; then
                    for test_mode in "paired" "single" "auto"; do
                        if run_test "$antibody" "$model" "$test_mode"; then
                            ((passed_tests++))
                        fi
                        ((total_tests++))
                    done
                fi
                ;;
                
            "traditional-only")
                # Only test traditional functionality
                if run_test "$antibody" "$model" "traditional"; then
                    ((passed_tests++))
                fi
                ((total_tests++))
                ;;
        esac
    done
    
    echo ""
    echo "$antibody Results: $passed_tests/$total_tests tests passed"
    echo ""
    
    # Generate individual report
    if command -v python3 >/dev/null 2>&1; then
        python3 bin/compare_paired_results.py --antibody "$antibody" --output "logs/paired/summaries/${antibody}_report.txt" 2>/dev/null || true
    fi
    
    return $((total_tests - passed_tests))  # Return number of failures
}

# Main execution
echo "Starting tests..."
echo ""

total_failures=0

case $MODE in
    "quick")
        echo "Quick mode: Testing core functionality only"
        models=("${PAIRED_MODELS[@]}" "${TRADITIONAL_MODELS[@]:0:1}")  # Just first traditional model
        ;;
    "full")
        echo "Full mode: Testing all models and modes"
        models=("${PAIRED_MODELS[@]}" "${TRADITIONAL_MODELS[@]}")
        ;;
    "paired-only")
        echo "Paired-only mode: Testing paired sequence models only"
        models=("${PAIRED_MODELS[@]}")
        ;;
    "traditional-only")
        echo "Traditional-only mode: Testing traditional models only"
        models=("${TRADITIONAL_MODELS[@]}")
        ;;
    *)
        echo "Unknown mode: $MODE"
        echo "Available modes: quick, full, paired-only, traditional-only"
        exit 1
        ;;
esac

echo "Models to test: ${models[*]}"
echo "Antibodies to test: ${ANTIBODIES[*]}"
echo ""

# Test each antibody
for antibody in "${ANTIBODIES[@]}"; do
    if ! test_antibody "$antibody" "${models[@]}"; then
        ((total_failures += $?))
    fi
done

# Generate overall summary
echo "OVERALL SUMMARY"
echo "==============="
echo "Mode: $MODE"
echo "Total antibodies tested: ${#ANTIBODIES[@]}"
echo "Models per antibody: ${#models[@]}"

if [ $total_failures -eq 0 ]; then
    echo "✓ All tests passed successfully!"
    exit_code=0
else
    echo "✗ $total_failures test(s) failed"
    exit_code=1
fi

echo ""
echo "Detailed logs available in: logs/paired/"
echo "Individual reports available in: logs/paired/summaries/"
echo ""
echo "To run a specific antibody test:"
echo "  bash bin/eval_paired_models.sh [antibody_name.py]"
echo ""
echo "To run a specific test manually:"
echo "  python3 bin/[antibody]_paired.py --model-name [model] --test-mode [mode]"

exit $exit_code
