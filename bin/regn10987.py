#!/usr/bin/env python3
"""
Template for simplified antibody paired sequence testing.
This template can be used to generate specific antibody testing scripts.
"""

from utils import *
from amis import get_model, encode, decode, reconstruct, compare
import time
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='REGN10987 paired sequence analysis')
    parser.add_argument('--namespace', type=str, default='REGN10987',
                        help='Model namespace')
    parser.add_argument('--model-name', type=str, default='igbert',
                        help='Type of language model (e.g., igbert, igt5, esm1b)')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    return parser.parse_args()

def test_paired_reconstruction(model, heavy_chain, light_chain, verbose=False):
    """Test paired sequence reconstruction."""
    
    print("Testing Paired Sequence Reconstruction")
    print("=" * 50)
    
    try:
        start_time = time.time()
        
        if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
            # Test paired sequence functionality
            print("1. Encoding paired sequences...")
            result = encode(heavy_chain=heavy_chain, light_chain=light_chain, model=model)
            
            print("2. Decoding paired sequences...")
            reconstructed = decode(result, model, exclude='unnatural')
            
            elapsed = time.time() - start_time
            print(f"   Time: {elapsed:.2f}s")
            
            if verbose:
                print(f"   Result type: {type(result)}")
                if isinstance(result, dict):
                    for chain_type, data in result.items():
                        print(f"   {chain_type} shape: {data.shape}")
            
            # Compare results
            if isinstance(reconstructed, dict):
                if 'heavy' in reconstructed:
                    print("\nHeavy Chain Analysis:")
                    compare(heavy_chain, reconstructed['heavy'], namespace='Heavy')
                
                if 'light' in reconstructed:
                    print("\nLight Chain Analysis:")
                    compare(light_chain, reconstructed['light'], namespace='Light')
            
            return {
                'success': True,
                'time': elapsed,
                'heavy_original': heavy_chain,
                'light_original': light_chain,
                'heavy_reconstructed': reconstructed.get('heavy', ''),
                'light_reconstructed': reconstructed.get('light', ''),
                'model_type': 'paired'
            }
            
        else:
            # Test traditional single-chain reconstruction
            print("Testing traditional single-chain reconstruction...")
            
            # Test heavy chain
            print("1. Heavy chain reconstruction...")
            heavy_reconstructed = reconstruct(heavy_chain=heavy_chain, model=model)
            
            # Test light chain  
            print("2. Light chain reconstruction...")
            light_reconstructed = reconstruct(light_chain=light_chain, model=model)
            
            elapsed = time.time() - start_time
            print(f"   Time: {elapsed:.2f}s")
            
            # Compare results
            print("\nHeavy Chain Analysis:")
            compare(heavy_chain, heavy_reconstructed, namespace='Heavy')
            
            print("\nLight Chain Analysis:")
            compare(light_chain, light_reconstructed, namespace='Light')
            
            return {
                'success': True,
                'time': elapsed,
                'heavy_original': heavy_chain,
                'light_original': light_chain,
                'heavy_reconstructed': heavy_reconstructed,
                'light_reconstructed': light_reconstructed,
                'model_type': 'traditional'
            }
            
    except Exception as e:
        print(f"Error during testing: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e),
            'time': time.time() - start_time
        }

def analyze_mutations(original, reconstructed, chain_type):
    """Analyze mutations between original and reconstructed sequences."""
    
    mutations = []
    min_len = min(len(original), len(reconstructed))
    
    for i in range(min_len):
        if original[i] != reconstructed[i]:
            mutations.append(f"{original[i]}{i+1}{reconstructed[i]}")
    
    print(f"\n{chain_type} Chain Mutation Analysis:")
    print(f"Original length: {len(original)}")
    print(f"Reconstructed length: {len(reconstructed)}")
    print(f"Mutations found: {len(mutations)}")
    
    if mutations:
        print(f"Mutations: {', '.join(mutations)}")
    else:
        print("No mutations detected")
    
    return mutations

if __name__ == '__main__':
    args = parse_args()

    print(f"REGN10987 Paired Sequence Analysis")
    print(f"Model: {args.model_name}")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    try:
        model = get_model(args)
        print(f"Model loaded: {model.name_}")
    except Exception as e:
        print(f"Failed to load model: {e}")
        exit(1)

    # REGN10987 sequences
    heavy_chain = "QVQLVESGGGVVQPGRSLRLSCAASGFTFSNYAMYWVRQAPGKGLEWVAVISYDGSNKYYADSVKGRFTISRDNSKNTLYLQMNSLRTEDTAVYYCASGSDYGDYLLVYWGQGTLVTVSS"
    light_chain = "QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLMIYDVSKRPSGVSNRFSGSKSGNTASLTISGLQSEDEADYYCNSLTSISTWVFGGGTKLTVL"

    print(f"Heavy chain length: {len(heavy_chain)}")
    print(f"Light chain length: {len(light_chain)}")

    # Run paired reconstruction test
    result = test_paired_reconstruction(model, heavy_chain, light_chain, args.verbose)

    # Analyze mutations if successful
    if result.get('success', False):
        if 'heavy_reconstructed' in result and result['heavy_reconstructed']:
            heavy_mutations = analyze_mutations(
                result['heavy_original'], 
                result['heavy_reconstructed'], 
                'Heavy'
            )
        
        if 'light_reconstructed' in result and result['light_reconstructed']:
            light_mutations = analyze_mutations(
                result['light_original'], 
                result['light_reconstructed'], 
                'Light'
            )

    print(f"\n" + "=" * 60)
    print("ANALYSIS COMPLETE")
    print("=" * 60)
    
    if result.get('success', False):
        print(f"✓ REGN10987 analysis completed successfully")
        print(f"  Execution time: {result.get('time', 0):.2f}s")
        print(f"  Model type: {result.get('model_type', 'unknown')}")
    else:
        print(f"✗ REGN10987 analysis failed")
        if 'error' in result:
            print(f"  Error: {result['error']}")
        exit(1)
