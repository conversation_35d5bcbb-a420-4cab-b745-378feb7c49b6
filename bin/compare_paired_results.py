#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to compare and analyze results from paired sequence testing.
Generates comprehensive reports comparing different testing modes.
"""

import os
import re
import argparse
from collections import defaultdict
import time

def parse_log_file(filepath):
    """Parse a log file and extract key information."""
    if not os.path.exists(filepath):
        return None
    
    try:
        with open(filepath, 'r') as f:
            content = f.read()
        
        result = {
            'filepath': filepath,
            'success': 'ANALYSIS COMPLETE' in content,
            'errors': [],
            'mutations': [],
            'timing': {},
            'model_info': {}
        }
        
        # Extract model information
        model_match = re.search(r'Model loaded: (.+)', content)
        if model_match:
            result['model_info']['name'] = model_match.group(1)
        
        # Extract timing information
        time_matches = re.findall(r'Time: ([\d.]+)s', content)
        if time_matches:
            result['timing']['times'] = [float(t) for t in time_matches]
            result['timing']['total'] = sum(result['timing']['times'])
            result['timing']['average'] = result['timing']['total'] / len(result['timing']['times'])
        
        # Extract mutations
        mutation_matches = re.findall(r'([A-Z])(\d+)([A-Z])', content)
        result['mutations'] = mutation_matches
        
        # Extract errors
        error_matches = re.findall(r'Error.*?: (.+)', content)
        result['errors'] = error_matches
        
        # Extract mode-specific success indicators
        result['modes'] = {
            'paired': 'PAIRED mode: SUCCESS' in content,
            'single': 'SINGLE mode: SUCCESS' in content,
            'auto': 'AUTO mode: SUCCESS' in content,
            'traditional': 'TRADITIONAL mode: SUCCESS' in content
        }
        
        return result
        
    except Exception as e:
        return {'filepath': filepath, 'error': str(e)}

def analyze_antibody_results(antibody_name, logs_dir):
    """Analyze all results for a specific antibody."""
    
    results = {
        'antibody': antibody_name,
        'models': {},
        'summary': {}
    }
    
    # Look for log files for this antibody
    log_pattern = f'reconstruct_{antibody_name}_'
    
    if not os.path.exists(logs_dir):
        return results
    
    for filename in os.listdir(logs_dir):
        if filename.startswith(log_pattern) and filename.endswith('.log'):
            filepath = os.path.join(logs_dir, filename)
            
            # Extract model and mode from filename
            # Format: reconstruct_antibody_model_mode.log
            parts = filename.replace('.log', '').split('_')
            if len(parts) >= 4:
                model = parts[2]
                mode = parts[3] if len(parts) > 3 else 'unknown'
                
                if model not in results['models']:
                    results['models'][model] = {}
                
                log_data = parse_log_file(filepath)
                if log_data:
                    results['models'][model][mode] = log_data
    
    # Generate summary statistics
    results['summary'] = generate_summary(results['models'])
    
    return results

def generate_summary(models_data):
    """Generate summary statistics from model results."""
    
    summary = {
        'total_tests': 0,
        'successful_tests': 0,
        'failed_tests': 0,
        'models_tested': len(models_data),
        'modes_tested': set(),
        'timing_stats': {},
        'error_summary': defaultdict(int)
    }
    
    all_times = []
    
    for model, modes in models_data.items():
        for mode, data in modes.items():
            summary['total_tests'] += 1
            summary['modes_tested'].add(mode)
            
            if data.get('success', False):
                summary['successful_tests'] += 1
            else:
                summary['failed_tests'] += 1
            
            # Collect timing data
            if 'timing' in data and 'times' in data['timing']:
                all_times.extend(data['timing']['times'])
            
            # Collect errors
            for error in data.get('errors', []):
                summary['error_summary'][error] += 1
    
    # Calculate timing statistics
    if all_times:
        summary['timing_stats'] = {
            'min': min(all_times),
            'max': max(all_times),
            'average': sum(all_times) / len(all_times),
            'total': sum(all_times)
        }
    
    summary['modes_tested'] = list(summary['modes_tested'])
    summary['success_rate'] = (summary['successful_tests'] / summary['total_tests'] * 100) if summary['total_tests'] > 0 else 0
    
    return summary

def generate_report(results, output_file):
    """Generate a comprehensive report."""
    
    with open(output_file, 'w') as f:
        f.write("=" * 80 + "\n")
        f.write(f"PAIRED SEQUENCE TESTING REPORT - {results['antibody'].upper()}\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Summary section
        summary = results['summary']
        f.write("SUMMARY\n")
        f.write("-" * 40 + "\n")
        f.write(f"Total tests run: {summary['total_tests']}\n")
        f.write(f"Successful tests: {summary['successful_tests']}\n")
        f.write(f"Failed tests: {summary['failed_tests']}\n")
        f.write(f"Success rate: {summary['success_rate']:.1f}%\n")
        f.write(f"Models tested: {summary['models_tested']}\n")
        f.write(f"Modes tested: {', '.join(summary['modes_tested'])}\n\n")
        
        # Timing statistics
        if summary['timing_stats']:
            f.write("TIMING STATISTICS\n")
            f.write("-" * 40 + "\n")
            timing = summary['timing_stats']
            f.write(f"Fastest test: {timing['min']:.2f}s\n")
            f.write(f"Slowest test: {timing['max']:.2f}s\n")
            f.write(f"Average time: {timing['average']:.2f}s\n")
            f.write(f"Total time: {timing['total']:.2f}s\n\n")
        
        # Model-by-model results
        f.write("DETAILED RESULTS BY MODEL\n")
        f.write("-" * 40 + "\n")
        
        for model, modes in results['models'].items():
            f.write(f"\n{model.upper()}\n")
            f.write("." * 20 + "\n")
            
            for mode, data in modes.items():
                status = "SUCCESS" if data.get('success', False) else "FAILED"
                f.write(f"  {mode}: {status}")
                
                if 'timing' in data and 'total' in data['timing']:
                    f.write(f" ({data['timing']['total']:.2f}s)")
                
                if data.get('errors'):
                    f.write(f" - Errors: {len(data['errors'])}")
                
                f.write("\n")
                
                # Show errors if any
                for error in data.get('errors', []):
                    f.write(f"    Error: {error}\n")
        
        # Error summary
        if summary['error_summary']:
            f.write("\nERROR SUMMARY\n")
            f.write("-" * 40 + "\n")
            for error, count in summary['error_summary'].items():
                f.write(f"{count}x: {error}\n")
        
        # Recommendations
        f.write("\nRECOMMENDations\n")
        f.write("-" * 40 + "\n")
        
        if summary['success_rate'] >= 90:
            f.write("✓ Excellent: Most tests passed successfully.\n")
        elif summary['success_rate'] >= 70:
            f.write("⚠ Good: Most tests passed, but some issues detected.\n")
        else:
            f.write("✗ Poor: Many tests failed. Check model compatibility and setup.\n")
        
        # Model-specific recommendations
        paired_models = ['igbert', 'igt5']
        traditional_models = ['esm1b', 'esm1v1', 'esm2-150M', 'esm2-650M']
        
        tested_paired = any(model in results['models'] for model in paired_models)
        tested_traditional = any(model in results['models'] for model in traditional_models)
        
        if tested_paired:
            f.write("✓ Paired sequence models (IgBert/IgT5) were tested.\n")
        else:
            f.write("⚠ Consider testing paired sequence models (IgBert/IgT5).\n")
        
        if tested_traditional:
            f.write("✓ Traditional models were tested for comparison.\n")
        else:
            f.write("⚠ Consider testing traditional models for comparison.\n")
        
        f.write("\n" + "=" * 80 + "\n")

def main():
    parser = argparse.ArgumentParser(description='Compare paired sequence testing results')
    parser.add_argument('--antibody', type=str, required=True,
                        help='Antibody name to analyze')
    parser.add_argument('--logs-dir', type=str, default='logs/paired',
                        help='Directory containing log files')
    parser.add_argument('--output', type=str,
                        help='Output file for report (default: logs/paired/{antibody}_comparison_report.txt)')
    
    args = parser.parse_args()
    
    if not args.output:
        args.output = f'{args.logs_dir}/{args.antibody}_comparison_report.txt'
    
    print(f"Analyzing results for {args.antibody}...")
    print(f"Looking in: {args.logs_dir}")
    
    # Analyze results
    results = analyze_antibody_results(args.antibody, args.logs_dir)
    
    if not results['models']:
        print(f"No log files found for {args.antibody} in {args.logs_dir}")
        return
    
    # Generate report
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    generate_report(results, args.output)
    
    print(f"Report generated: {args.output}")
    print(f"Summary: {results['summary']['successful_tests']}/{results['summary']['total_tests']} tests passed ({results['summary']['success_rate']:.1f}%)")

if __name__ == '__main__':
    main()
