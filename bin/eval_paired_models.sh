#!/bin/bash

# Script to evaluate antibody models with paired sequence functionality
# Usage: bash bin/eval_paired_models.sh [antibody_name.py]

if [ $# -eq 0 ]; then
    echo "Usage: bash bin/eval_paired_models.sh [antibody_name.py]"
    echo "Available antibodies: medi8852.py, medi_uca.py, mab114.py, mab114_uca.py, s309.py, regn10987.py, c143.py"
    exit 1
fi

# Extract antibody name without .py extension
antibody_name=$(basename "$1" .py)

echo "Evaluating paired sequence functionality for $antibody_name"
echo "============================================================"

# Create logs directory if it doesn't exist
mkdir -p logs/paired

# Models that support paired sequences
declare -a paired_models=( "igbert" "igt5" )

# Traditional models for comparison
declare -a traditional_models=( "esm1b" "esm1v1" "esm2-150M" "esm2-650M" )

echo "Testing paired sequence models..."
for model in "${paired_models[@]}"
do
    echo "Running $model with paired sequences..."
    python3 bin/${antibody_name}_paired.py --model-name $model --test-mode paired > logs/paired/reconstruct_${antibody_name}_${model}_paired.log 2>&1
    
    echo "Running $model with single sequences..."
    python3 bin/${antibody_name}_paired.py --model-name $model --test-mode single > logs/paired/reconstruct_${antibody_name}_${model}_single.log 2>&1
    
    echo "Running $model with auto-pairing..."
    python3 bin/${antibody_name}_paired.py --model-name $model --test-mode auto > logs/paired/reconstruct_${antibody_name}_${model}_auto.log 2>&1
done

echo "Testing traditional models for comparison..."
for model in "${traditional_models[@]}"
do
    echo "Running $model (traditional)..."
    python3 bin/${antibody_name}_paired.py --model-name $model --test-mode traditional > logs/paired/reconstruct_${antibody_name}_${model}_traditional.log 2>&1
done

echo "Generating comparison report..."
python3 bin/compare_paired_results.py --antibody $antibody_name --output logs/paired/${antibody_name}_comparison_report.txt

echo "Evaluation complete! Check logs/paired/ for detailed results."
echo "Summary report: logs/paired/${antibody_name}_comparison_report.txt"
