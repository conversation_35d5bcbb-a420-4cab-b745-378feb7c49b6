import torch
import numpy as np
import warnings
from transformers import T5EncoderModel, T5Tokenizer

class IgT5Model(object):
    def __init__(self, name='Exscientia/IgT5'):
        self.name_ = name
        
        with warnings.catch_warnings():
            warnings.simplefilter('ignore', UserWarning)
            self.tokenizer_ = T5Tokenizer.from_pretrained(name, do_lower_case=False)
            self.model_ = T5EncoderModel.from_pretrained(name)
        
        self.model_.eval()
        if torch.cuda.is_available():
            self.model_ = self.model_.cuda()
        
        # Create a simple alphabet-like interface for compatibility
        self.alphabet_ = SimpleAlphabet()
        self.offset_ = 1

    def predict_sequence_prob(self, seq, light_chain=None):
        """
        Use IgT5's encoder to get embeddings. Since T5 encoder doesn't have MLM,
        we'll return embeddings that can be used for similarity-based reconstruction.
        """
        if light_chain is None:
            # Use a canonical light chain sequence
            light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"
        
        # Format as paired sequence with spaces between amino acids and </s> separator
        paired_seq = ' '.join(seq) + ' </s> ' + ' '.join(light_chain)
        
        # Tokenize the sequence
        tokens = self.tokenizer_.batch_encode_plus(
            [paired_seq],
            add_special_tokens=True,
            padding=True,
            return_tensors="pt",
            return_special_tokens_mask=True
        )
        
        if torch.cuda.is_available():
            tokens = {k: v.cuda() for k, v in tokens.items()}
        
        # Get embeddings from T5 encoder
        with torch.no_grad():
            output = self.model_(
                input_ids=tokens['input_ids'],
                attention_mask=tokens['attention_mask']
            )
            embeddings = output.last_hidden_state[0]  # Remove batch dimension
            
            # Find the </s> token to separate heavy and light chains
            eos_token_id = self.tokenizer_.eos_token_id
            eos_positions = (tokens['input_ids'][0] == eos_token_id).nonzero(as_tuple=True)[0]
            
            if len(eos_positions) > 0:
                # Take embeddings up to the first </s> token
                heavy_embeddings = embeddings[:eos_positions[0]]
            else:
                # Fallback: take all embeddings except the last one
                heavy_embeddings = embeddings[:-1]
            
            # Truncate or pad to match input sequence length
            if heavy_embeddings.shape[0] > len(seq):
                heavy_embeddings = heavy_embeddings[:len(seq)]
            elif heavy_embeddings.shape[0] < len(seq):
                # Pad with zeros if needed
                embed_dim = heavy_embeddings.shape[1]
                padding = torch.zeros(len(seq) - heavy_embeddings.shape[0], embed_dim)
                if torch.cuda.is_available():
                    padding = padding.cuda()
                heavy_embeddings = torch.cat([heavy_embeddings, padding], dim=0)
        
        return heavy_embeddings.cpu().numpy()

    def encode(self, seq):
        """
        Encode a sequence using IgT5 embeddings
        """
        return self.predict_sequence_prob(seq)

    def decode(self, embeddings):
        """
        For IgT5, we don't have direct MLM capabilities like BERT.
        We'll use a similarity-based approach to find the most likely amino acids.
        This is a simplified approach for compatibility.
        """
        # Since T5 encoder doesn't have MLM, we'll use a simple approach
        # based on embedding similarity to canonical amino acid embeddings
        
        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']
        
        # Get embeddings for individual amino acids
        aa_embeddings = []
        for aa in amino_acids:
            tokens = self.tokenizer_.encode(aa, return_tensors="pt", add_special_tokens=False)
            if torch.cuda.is_available():
                tokens = tokens.cuda()
            
            with torch.no_grad():
                output = self.model_(input_ids=tokens)
                aa_embedding = output.last_hidden_state[0, 0]  # First token embedding
                aa_embeddings.append(aa_embedding.cpu().numpy())
        
        aa_embeddings = np.stack(aa_embeddings)  # Shape: (20, embed_dim)
        
        # For each position, find the most similar amino acid
        predicted_sequence = []
        for i in range(embeddings.shape[0]):
            pos_embedding = embeddings[i]  # Shape: (embed_dim,)
            
            # Compute cosine similarity with all amino acid embeddings
            similarities = np.dot(aa_embeddings, pos_embedding) / (
                np.linalg.norm(aa_embeddings, axis=1) * np.linalg.norm(pos_embedding)
            )
            
            # Get the amino acid with highest similarity
            best_aa_idx = np.argmax(similarities)
            predicted_sequence.append(amino_acids[best_aa_idx])
        
        return ''.join(predicted_sequence)


class SimpleAlphabet(object):
    """
    Simple alphabet class for compatibility with ESM interface
    """
    def __init__(self):
        # Standard amino acid alphabet
        self.all_toks = [
            '<cls>', '<pad>', '<eos>', '<unk>',
            'L', 'A', 'G', 'V', 'S', 'E', 'R', 'T', 'I', 'D', 'P', 'K',
            'Q', 'N', 'F', 'Y', 'M', 'H', 'W', 'C', 'X', 'B', 'U', 'Z', 'O', '.', '-'
        ]
        self.tok_to_idx = {tok: i for i, tok in enumerate(self.all_toks)}
        self.padding_idx = self.tok_to_idx['<pad>']
        
    def get_idx(self, tok):
        return self.tok_to_idx.get(tok, self.tok_to_idx['<unk>'])
        
    def get_tok(self, idx):
        return self.all_toks[idx] if 0 <= idx < len(self.all_toks) else '<unk>'
