#!/bin/bash

# <PERSON>ript to test all antibodies with the simplified paired sequence interface
# Usage: bash bin/test_all_antibodies_simple.sh [model_name]

MODEL=${1:-"igbert"}

echo "Testing All Antibodies with Simplified Interface"
echo "================================================"
echo "Model: $MODEL"
echo "Timestamp: $(date)"
echo ""

# List of antibodies
ANTIBODIES=("mab114" "medi8852" "s309" "regn10987" "c143" "medi_uca" "mab114_uca")

# Create logs directory
mkdir -p logs/simple

total_tests=0
passed_tests=0

for antibody in "${ANTIBODIES[@]}"; do
    echo "Testing $antibody..."
    echo "$(printf '=%.0s' {1..30})"
    
    log_file="logs/simple/${antibody}_${MODEL}.log"
    
    if python3 bin/${antibody}.py --model-name $MODEL > "$log_file" 2>&1; then
        echo "  ✓ $antibody PASSED"
        ((passed_tests++))
    else
        echo "  ✗ $antibody FAILED (check $log_file)"
    fi
    
    ((total_tests++))
    echo ""
done

echo "SUMMARY"
echo "======="
echo "Total tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"
echo "Success rate: $(echo "scale=1; $passed_tests * 100 / $total_tests" | bc)%"
echo ""
echo "Detailed logs available in: logs/simple/"
