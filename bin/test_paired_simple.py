#!/usr/bin/env python3
"""
Simplified paired sequence testing for antibodies.
Tests the new paired sequence functionality with both heavy and light chains.
"""

from utils import *
from amis import get_model, encode, decode, reconstruct, compare
import time
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='Simple paired sequence testing')
    parser.add_argument('--antibody', type=str, default='mab114',
                        choices=['mab114', 'medi8852', 's309', 'regn10987', 'c143', 'medi_uca', 'mab114_uca'],
                        help='Antibody to test')
    parser.add_argument('--model-name', type=str, default='igbert',
                        help='Model to use (igbert, igt5, esm1b, etc.)')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    return parser.parse_args()

def get_antibody_sequences(antibody_name):
    """Get heavy and light chain sequences for the specified antibody."""
    
    sequences = {
        'mab114': {
            'heavy': 'EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMHWVRQTIDKRLEWVSAVGPSGDTYYADSVKGRFAVSRENAKNSLSLQMNSLTAGDTAIYYCVRSDRGVAGLFDSWGQGILVTVSS',
            'light': 'DIQMTQSPSSLSASVGDRITITCRASQAFDNYVAWYQQRPGKVPKLLISAASALHAGVPSRFSGSGSGTHFTLTISSLQPEDVATYYCQNYNSAPLTFGGGTKVEIK'
        },
        'medi8852': {
            'heavy': 'QVQLQQSGPGLVKPSQTLSLTCAISGDSVSSYNAVWNWIRQSPSRGLEWLGRTYYRSGWYNDYAESVKSRITINPDTSKNQFSLQLNSVTPEDTAVYYCARSGHITVFGVNVDAFDMWGQGTMVTVSS',
            'light': 'DIQMTQSPSSLSASVGDRVTITCRTSQSLSSYTHWYQQKPGKAPKLLIYAASSRGSGVPSRFSGSGSGTDFTLTISSLQPEDFATYYCQQSRTFGQGTKVEIK'
        },
        's309': {
            'heavy': 'QVQLVQSGAEVKKPGASVKVSCKASGYPFTSYGISWVRQAPGQGLEWMGWISTYNGNTNYAQKFQGRVTMTTDTSTTTGYMELRRLRSDDTAVYYCARDYTRGAWFGESLIGGFDNWGQGTLVTVSS',
            'light': 'EIVLTQSPGTLSLSPGERATLSCRASQTVSSTSLAWYQQKPGQAPRLLIYGASSRATGIPDRFSGSGSGTDFTLTISRLEPEDFAVYYCQQHDTSLTFGGGTKVEIK'
        },
        'regn10987': {
            'heavy': 'QVQLVESGGGVVQPGRSLRLSCAASGFTFSNYAMYWVRQAPGKGLEWVAVISYDGSNKYYADSVKGRFTISRDNSKNTLYLQMNSLRTEDTAVYYCASGSDYGDYLLVYWGQGTLVTVSS',
            'light': 'QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLMIYDVSKRPSGVSNRFSGSKSGNTASLTISGLQSEDEADYYCNSLTSISTWVFGGGTKLTVL'
        },
        'c143': {
            'heavy': 'EVQLVESGGGLVQPGGSLRLSCAASGFSVSTKYMTWVRQAPGKGLEWVSVLYSGGSDYYADSVKGRFTISRDNSKNALYLQMNSLRVEDTGVYYCARDSSEVRDHPGHPGRSVGAFDIWGQGTMVTVSS',
            'light': 'QSALTQPASVSGSPGQSITISCTGTSNDVGSYTLVSWYQQYPGKAPKLLIFEGTKRSSGISNRFSGSKSGNTASLTISGLQGEDEADYYCCSYAGASTFVFGGGTKLTVL'
        },
        'medi_uca': {
            'heavy': 'QVQLQQSGPGLVKPSQTLSLTCAISGDSVSSNSAAWNWIRQSPSRGLEWLGRTYYRSKWYNDYAVSVKSRITINPDTSKNQFSLQLNSVTPEDTAVYYCARGGHITIFGVNIDAFDIWGQGTMVTVSS',
            'light': 'DIQMTQSPSSLSASVGDRVTITCRASQSISSYLNWYQQKPGKAPKLLIYAASSLQSGVPSRFSGSGSGTDFTLTISSLQPEDFATYYCQQSRTFGQGTKVEIK'
        },
        'mab114_uca': {
            'heavy': 'EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYDMHWVRQATGKGLEWVSAIGTAGDTYYPGSVKGRFTISRENAKNSLYLQMNSLRAGDTAVYYCVRSDRGVAGLFDSWGQGTLVTVSS',
            'light': 'DIQMTQSPSSLSASVGDRVTITCRASQGISNYLAWYQQKPGKVPKLLIYAASTLQSGVPSRFSGSGSGTDFTLTISSLQPEDVATYYCQKYNSAPLTFGGGTKVEIK'
        }
    }
    
    return sequences.get(antibody_name, sequences['mab114'])

def test_paired_reconstruction(model, heavy_chain, light_chain, verbose=False):
    """Test paired sequence reconstruction."""
    
    print("Testing Paired Sequence Reconstruction")
    print("=" * 50)
    
    try:
        start_time = time.time()
        
        if model.name_ in ['Exscientia/IgBert', 'Exscientia/IgT5']:
            # Test paired sequence functionality
            print("1. Encoding paired sequences...")
            embeddings = encode(heavy_chain=heavy_chain, light_chain=light_chain, model=model)
            
            print("2. Decoding paired sequences...")
            reconstructed = decode(embeddings, model, exclude='unnatural')
            
            elapsed = time.time() - start_time
            print(f"   Time: {elapsed:.2f}s")
            
            if verbose:
                print(f"   Embeddings type: {type(embeddings)}")
                if isinstance(embeddings, dict):
                    for chain_type, emb in embeddings.items():
                        print(f"   {chain_type} shape: {emb.shape}")
            
            # Compare results
            if isinstance(reconstructed, dict):
                if 'heavy' in reconstructed:
                    print("\nHeavy Chain Comparison:")
                    compare(heavy_chain, reconstructed['heavy'], namespace='Heavy')
                
                if 'light' in reconstructed:
                    print("\nLight Chain Comparison:")
                    compare(light_chain, reconstructed['light'], namespace='Light')
            
            return {
                'success': True,
                'time': elapsed,
                'heavy_original': heavy_chain,
                'light_original': light_chain,
                'heavy_reconstructed': reconstructed.get('heavy', ''),
                'light_reconstructed': reconstructed.get('light', ''),
                'embeddings_type': type(embeddings).__name__
            }
            
        else:
            # Test traditional single-chain reconstruction
            print("Testing traditional single-chain reconstruction...")
            
            # Test heavy chain
            print("1. Heavy chain reconstruction...")
            heavy_reconstructed = reconstruct(heavy_chain=heavy_chain, model=model)
            
            # Test light chain  
            print("2. Light chain reconstruction...")
            light_reconstructed = reconstruct(light_chain=light_chain, model=model)
            
            elapsed = time.time() - start_time
            print(f"   Time: {elapsed:.2f}s")
            
            # Compare results
            print("\nHeavy Chain Comparison:")
            compare(heavy_chain, heavy_reconstructed, namespace='Heavy')
            
            print("\nLight Chain Comparison:")
            compare(light_chain, light_reconstructed, namespace='Light')
            
            return {
                'success': True,
                'time': elapsed,
                'heavy_original': heavy_chain,
                'light_original': light_chain,
                'heavy_reconstructed': heavy_reconstructed,
                'light_reconstructed': light_reconstructed,
                'model_type': 'traditional'
            }
            
    except Exception as e:
        print(f"Error during testing: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e),
            'time': time.time() - start_time
        }

def test_blank_sequences(model, verbose=False):
    """Test with blank sequences to verify default behavior."""
    
    print("\nTesting Blank Sequence Handling")
    print("=" * 50)
    
    if model.name_ not in ['Exscientia/IgBert', 'Exscientia/IgT5']:
        print("Skipping blank sequence test for traditional model")
        return {'success': True, 'skipped': True}
    
    try:
        start_time = time.time()
        
        print("1. Testing with blank sequences...")
        embeddings = encode(heavy_chain="", light_chain="", model=model)
        
        print("2. Testing with only heavy chain...")
        embeddings_heavy = encode(heavy_chain="EVQLVESGGGLIQPGGSLRLSCAASGFALRMYDMH", light_chain="", model=model)
        
        print("3. Testing with only light chain...")
        embeddings_light = encode(heavy_chain="", light_chain="DIQMTQSPSSLSASVGDRITITCRASQAFDNYVA", model=model)
        
        elapsed = time.time() - start_time
        print(f"   Time: {elapsed:.2f}s")
        
        if verbose:
            print(f"   Blank embeddings: {type(embeddings)}")
            print(f"   Heavy-only embeddings: {type(embeddings_heavy)}")
            print(f"   Light-only embeddings: {type(embeddings_light)}")
        
        return {
            'success': True,
            'time': elapsed,
            'blank_handled': True
        }
        
    except Exception as e:
        print(f"Error during blank sequence testing: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e)
        }

def main():
    args = parse_args()
    
    print(f"Simplified Paired Sequence Testing")
    print(f"Antibody: {args.antibody}")
    print(f"Model: {args.model_name}")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Load model
    try:
        model = get_model(args)
        print(f"Model loaded: {model.name_}")
    except Exception as e:
        print(f"Failed to load model: {e}")
        return 1
    
    # Get antibody sequences
    sequences = get_antibody_sequences(args.antibody)
    heavy_chain = sequences['heavy']
    light_chain = sequences['light']
    
    print(f"Heavy chain length: {len(heavy_chain)}")
    print(f"Light chain length: {len(light_chain)}")
    
    # Run tests
    results = {}
    
    # Test paired reconstruction
    results['paired'] = test_paired_reconstruction(model, heavy_chain, light_chain, args.verbose)
    
    # Test blank sequences
    results['blank'] = test_blank_sequences(model, args.verbose)
    
    # Summary
    print("\n" + "=" * 60)
    print("TESTING SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r.get('success', False))
    
    print(f"Tests run: {total_tests}")
    print(f"Tests passed: {passed_tests}")
    print(f"Success rate: {passed_tests/total_tests*100:.1f}%")
    
    for test_name, result in results.items():
        status = "PASS" if result.get('success', False) else "FAIL"
        print(f"{test_name}: {status}")
        if not result.get('success', False) and 'error' in result:
            print(f"  Error: {result['error']}")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == '__main__':
    exit(main())
