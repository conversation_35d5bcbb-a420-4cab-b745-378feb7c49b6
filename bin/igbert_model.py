import torch
import numpy as np
import warnings
from transformers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BertForMaskedLM

class IgBertModel(object):
    def __init__(self, name='Exscientia/IgBert'):
        self.name_ = name

        with warnings.catch_warnings():
            warnings.simplefilter('ignore', UserWarning)
            self.tokenizer_ = BertTokenizer.from_pretrained(name, do_lower_case=False)
            self.model_ = BertForMaskedLM.from_pretrained(name)

        self.model_.eval()
        if torch.cuda.is_available():
            self.model_ = self.model_.cuda()

        # Create a simple alphabet-like interface for compatibility
        self.alphabet_ = SimpleAlphabet()
        self.offset_ = 1

        # Default canonical sequences
        self.default_heavy = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
        self.default_light = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"

    def predict_sequence_prob(self, heavy_chain="", light_chain=""):
        """
        Use IgBert's masked language modeling to predict mutations for paired sequences.

        Args:
            heavy_chain: Heavy chain sequence (default: empty string)
            light_chain: Light chain sequence (default: empty string)

        Returns:
            Dictionary with 'heavy' and 'light' logits for both chains
        """
        # Format as paired sequence with spaces between amino acids and [SEP] separator
        # The tokenizer uses [SEP] to separate sequences
        heavy_spaced = ' '.join(heavy_chain) if heavy_chain else ""
        light_spaced = ' '.join(light_chain) if light_chain else ""
        paired_seq = heavy_spaced + ' [SEP] ' + light_spaced

        # Tokenize the sequence
        tokens = self.tokenizer_.batch_encode_plus(
            [paired_seq],
            add_special_tokens=True,
            padding=True,
            return_tensors="pt",
            return_special_tokens_mask=True
        )

        if torch.cuda.is_available():
            tokens = {k: v.cuda() for k, v in tokens.items()}

        # Get logits for all positions
        with torch.no_grad():
            output = self.model_(
                input_ids=tokens['input_ids'],
                attention_mask=tokens['attention_mask']
            )
            logits = output.logits[0]  # Remove batch dimension

            # Find the [SEP] token to separate heavy and light chains
            sep_token_id = self.tokenizer_.sep_token_id
            input_ids = tokens['input_ids'][0]
            sep_positions = []
            for i, token_id in enumerate(input_ids):
                if token_id == sep_token_id:
                    sep_positions.append(i)
            sep_positions = torch.tensor(sep_positions)

            result = {}

            if len(sep_positions) > 0:
                sep_pos = sep_positions[0].item()
                # Heavy chain logits (excluding [CLS] and up to first [SEP])
                heavy_logits = logits[1:sep_pos]  # Skip [CLS] token
                # Light chain logits (after [SEP] token, excluding final tokens)
                light_logits = logits[sep_pos+1:-1]  # Skip [SEP] and final tokens
            else:
                # Fallback: split logits in half
                mid_point = logits.shape[0] // 2
                heavy_logits = logits[1:mid_point]
                light_logits = logits[mid_point:-1]

            # Adjust logits to match sequence lengths
            if heavy_chain:
                if heavy_logits.shape[0] > len(heavy_chain):
                    heavy_logits = heavy_logits[:len(heavy_chain)]
                elif heavy_logits.shape[0] < len(heavy_chain):
                    vocab_size = heavy_logits.shape[1]
                    padding = torch.zeros(len(heavy_chain) - heavy_logits.shape[0], vocab_size)
                    if torch.cuda.is_available():
                        padding = padding.cuda()
                    heavy_logits = torch.cat([heavy_logits, padding], dim=0)
                result['heavy'] = heavy_logits.cpu().numpy()

            if light_chain:
                if light_logits.shape[0] > len(light_chain):
                    light_logits = light_logits[:len(light_chain)]
                elif light_logits.shape[0] < len(light_chain):
                    vocab_size = light_logits.shape[1]
                    padding = torch.zeros(len(light_chain) - light_logits.shape[0], vocab_size)
                    if torch.cuda.is_available():
                        padding = padding.cuda()
                    light_logits = torch.cat([light_logits, padding], dim=0)
                result['light'] = light_logits.cpu().numpy()

        return result

    def encode(self, heavy_chain="", light_chain=""):
        """
        Encode paired sequences using IgBert embeddings

        Args:
            heavy_chain: Heavy chain sequence (default: empty string)
            light_chain: Light chain sequence (default: empty string)

        Returns:
            Dictionary with 'heavy' and 'light' logits for both chains
        """
        return self.predict_sequence_prob(heavy_chain=heavy_chain, light_chain=light_chain)

    def decode(self, logits_dict):
        """
        Convert IgBert logits to amino acid sequences.

        Args:
            logits_dict: Dictionary with 'heavy' and/or 'light' logits

        Returns:
            Dictionary with 'heavy' and/or 'light' sequences
        """
        # Map IgBert tokenizer vocabulary to amino acids
        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']

        # Get token IDs for amino acids
        aa_token_ids = []
        for aa in amino_acids:
            token_id = self.tokenizer_.convert_tokens_to_ids(aa)
            aa_token_ids.append(token_id)

        result = {}

        for chain_type, logits in logits_dict.items():
            # Extract logits for amino acid tokens only
            aa_logits = logits[:, aa_token_ids]

            # Get the most likely amino acid for each position
            predicted_indices = np.argmax(aa_logits, axis=1)
            predicted_sequence = ''.join([amino_acids[idx] for idx in predicted_indices])

            result[chain_type] = predicted_sequence

        return result




class SimpleAlphabet(object):
    """
    Simple alphabet class for compatibility with ESM interface
    """
    def __init__(self):
        # Standard amino acid alphabet
        self.all_toks = [
            '<cls>', '<pad>', '<eos>', '<unk>',
            'L', 'A', 'G', 'V', 'S', 'E', 'R', 'T', 'I', 'D', 'P', 'K',
            'Q', 'N', 'F', 'Y', 'M', 'H', 'W', 'C', 'X', 'B', 'U', 'Z', 'O', '.', '-'
        ]
        self.tok_to_idx = {tok: i for i, tok in enumerate(self.all_toks)}
        self.padding_idx = self.tok_to_idx['<pad>']

    def get_idx(self, tok):
        return self.tok_to_idx.get(tok, self.tok_to_idx['<unk>'])

    def get_tok(self, idx):
        return self.all_toks[idx] if 0 <= idx < len(self.all_toks) else '<unk>'
