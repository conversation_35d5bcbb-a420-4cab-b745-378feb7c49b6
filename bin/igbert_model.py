import torch
import numpy as np
import warnings
from transformers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BertForMaskedLM

class IgBertModel(object):
    def __init__(self, name='Exscientia/IgBert'):
        self.name_ = name

        with warnings.catch_warnings():
            warnings.simplefilter('ignore', UserWarning)
            self.tokenizer_ = BertTokenizer.from_pretrained(name, do_lower_case=False)
            self.model_ = BertForMaskedLM.from_pretrained(name)

        self.model_.eval()
        if torch.cuda.is_available():
            self.model_ = self.model_.cuda()

        # Create a simple alphabet-like interface for compatibility
        self.alphabet_ = SimpleAlphabet()
        self.offset_ = 1

        # Default canonical sequences
        self.default_heavy = "QVQLVQSGAEVKKPGASVKVSCKASGYTFTGYYMHWVRQAPGQGLEWMGWINPNSGGTNYAQKFQGRVTMTRDTSISTAYMELSRLRSDDTAVYYCAR"
        self.default_light = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"

    def predict_sequence_prob(self, seq, light_chain=None, heavy_chain=None, is_heavy=True):
        """
        Use IgBert's masked language modeling to predict mutations.
        Supports multiple input modes:
        1. Paired sequences: provide both heavy_chain and light_chain
        2. Single sequence with pairing: provide seq + light_chain (if seq is heavy) or seq + heavy_chain (if seq is light)
        3. Single sequence with auto-pairing: provide seq + is_heavy flag, auto-pair with default

        Args:
            seq: The primary input sequence to analyze
            light_chain: Optional light chain sequence for pairing
            heavy_chain: Optional heavy chain sequence for pairing
            is_heavy: If True, treat seq as heavy chain; if False, treat as light chain
                     Only used when neither light_chain nor heavy_chain is provided
        """
        # Handle different input scenarios
        if heavy_chain is not None and light_chain is not None:
            # Scenario 1: Both chains provided - seq is ignored, use the provided pair
            target_heavy = heavy_chain
            target_light = light_chain
            target_seq = heavy_chain  # Return logits for heavy chain by default
        elif heavy_chain is not None:
            # Scenario 2: Heavy chain provided, seq should be light chain
            target_heavy = heavy_chain
            target_light = seq
            target_seq = seq  # Return logits for the input sequence (light chain)
            is_heavy = False
        elif light_chain is not None:
            # Scenario 3: Light chain provided, seq should be heavy chain
            target_heavy = seq
            target_light = light_chain
            target_seq = seq  # Return logits for the input sequence (heavy chain)
            is_heavy = True
        else:
            # Scenario 4: Single sequence, auto-pair based on is_heavy flag
            if is_heavy:
                target_heavy = seq
                target_light = self.default_light
                target_seq = seq
            else:
                target_heavy = self.default_heavy
                target_light = seq
                target_seq = seq

        # Format as paired sequence with spaces between amino acids and </s> separator
        # The tokenizer expects input of the form ["V Q ... S S </s> E V ... I K", ...]
        paired_seq = ' '.join(target_heavy) + ' </s> ' + ' '.join(target_light)

        # Tokenize the sequence
        tokens = self.tokenizer_.batch_encode_plus(
            [paired_seq],
            add_special_tokens=True,
            padding=True,
            return_tensors="pt",
            return_special_tokens_mask=True
        )

        if torch.cuda.is_available():
            tokens = {k: v.cuda() for k, v in tokens.items()}

        # Get logits for all positions
        with torch.no_grad():
            output = self.model_(
                input_ids=tokens['input_ids'],
                attention_mask=tokens['attention_mask']
            )
            logits = output.logits[0]  # Remove batch dimension

            # Find the </s> token to separate heavy and light chains
            eos_token_id = self.tokenizer_.eos_token_id
            eos_positions = (tokens['input_ids'][0] == eos_token_id).nonzero(as_tuple=True)[0]

            if len(eos_positions) > 0:
                if is_heavy:
                    # Take logits for heavy chain (excluding [CLS] and up to first </s>)
                    target_logits = logits[1:eos_positions[0]]  # Skip [CLS] token
                else:
                    # Take logits for light chain (after </s> token)
                    target_logits = logits[eos_positions[0]+1:-1]  # Skip </s> and [SEP] tokens
            else:
                # Fallback: take all logits except special tokens
                target_logits = logits[1:-1]  # Skip [CLS] and [SEP]

            # Truncate or pad to match target sequence length
            if target_logits.shape[0] > len(target_seq):
                target_logits = target_logits[:len(target_seq)]
            elif target_logits.shape[0] < len(target_seq):
                # Pad with zeros if needed
                vocab_size = target_logits.shape[1]
                padding = torch.zeros(len(target_seq) - target_logits.shape[0], vocab_size)
                if torch.cuda.is_available():
                    padding = padding.cuda()
                target_logits = torch.cat([target_logits, padding], dim=0)

        return target_logits.cpu().numpy()

    def encode(self, seq, light_chain=None, heavy_chain=None, is_heavy=True):
        """
        Encode a sequence using IgBert embeddings

        Args:
            seq: The primary input sequence to analyze
            light_chain: Optional light chain sequence for pairing
            heavy_chain: Optional heavy chain sequence for pairing
            is_heavy: If True, treat seq as heavy chain; if False, treat as light chain
        """
        return self.predict_sequence_prob(seq, light_chain=light_chain, heavy_chain=heavy_chain, is_heavy=is_heavy)

    def decode(self, logits):
        """
        Convert IgBert logits to amino acid sequence.
        """
        # Map IgBert tokenizer vocabulary to amino acids
        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']

        # Get token IDs for amino acids
        aa_token_ids = []
        for aa in amino_acids:
            token_id = self.tokenizer_.convert_tokens_to_ids(aa)
            aa_token_ids.append(token_id)

        # Extract logits for amino acid tokens only
        aa_logits = logits[:, aa_token_ids]

        # Get the most likely amino acid for each position
        predicted_indices = np.argmax(aa_logits, axis=1)
        predicted_sequence = ''.join([amino_acids[idx] for idx in predicted_indices])

        return predicted_sequence




class SimpleAlphabet(object):
    """
    Simple alphabet class for compatibility with ESM interface
    """
    def __init__(self):
        # Standard amino acid alphabet
        self.all_toks = [
            '<cls>', '<pad>', '<eos>', '<unk>',
            'L', 'A', 'G', 'V', 'S', 'E', 'R', 'T', 'I', 'D', 'P', 'K',
            'Q', 'N', 'F', 'Y', 'M', 'H', 'W', 'C', 'X', 'B', 'U', 'Z', 'O', '.', '-'
        ]
        self.tok_to_idx = {tok: i for i, tok in enumerate(self.all_toks)}
        self.padding_idx = self.tok_to_idx['<pad>']

    def get_idx(self, tok):
        return self.tok_to_idx.get(tok, self.tok_to_idx['<unk>'])

    def get_tok(self, idx):
        return self.all_toks[idx] if 0 <= idx < len(self.all_toks) else '<unk>'
